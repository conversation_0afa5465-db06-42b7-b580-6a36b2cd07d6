import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import 'services/secure_storage_service.dart';
import 'services/logger_service.dart';
import 'services/first_launch_service.dart';
import 'theme/app_theme.dart';
import 'config/app_config.dart';
import 'localization/app_localizations.dart';
import 'providers/app_settings_provider.dart';
import 'providers/course_provider.dart';
import 'providers/user_provider.dart';
import 'providers/library_provider.dart';
import 'providers/career_guidance_provider.dart';

import 'screens/home/<USER>';
import 'screens/auth/login_screen.dart';
import 'screens/onboarding/language_selection_screen.dart';

// Global key to access the app state for restarting
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Create a stream controller for app restart events
final StreamController<void> _restartController = StreamController.broadcast();
Stream<void> get restartStream => _restartController.stream;

// Function to restart the app
void restartApp() {
  _restartController.add(null);
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure CachedNetworkImage for optimal performance
  CachedNetworkImage.logLevel = CacheManagerLogLevel.warning;

  // Optimize image cache settings for better performance with many thumbnails
  PaintingBinding.instance.imageCache.maximumSize =
      500; // Increased for more cached images
  PaintingBinding.instance.imageCache.maximumSizeBytes =
      150 * 1024 * 1024; // 150 MB

  // Set image cache eviction policy for better memory management
  PaintingBinding.instance.imageCache.currentSizeBytes;

  // Initialize app settings provider
  final appSettingsProvider = AppSettingsProvider();
  await appSettingsProvider.init();

  // Initialize course provider
  final courseProvider = CourseProvider();
  await courseProvider.init();

  // Initialize user provider
  final userProvider = UserProvider();
  await userProvider.init();

  // Initialize library provider
  final libraryProvider = LibraryProvider();
  await libraryProvider.init();

  // Initialize career guidance provider
  final careerGuidanceProvider = CareerGuidanceProvider();
  await careerGuidanceProvider.init();

  // Set up callback to load career results after login
  userProvider.setOnLoginSuccessCallback(() {
    careerGuidanceProvider.loadResultsAfterLogin();
    // Also fetch enrolled courses when user logs in
    courseProvider.fetchEnrolledCoursesWithoutContext(forceRefresh: true);
  });

  // Set up callback to clear career data when user changes
  userProvider.setOnUserChangeCallback(() {
    careerGuidanceProvider.clearUserData();
    // Clear course data when user changes
    courseProvider.clearUserData();
  });

  // Set up callback to reload career data when language changes
  appSettingsProvider.setOnLanguageChangeCallback((String newLanguageCode) {
    careerGuidanceProvider.reloadDataForLanguageChange(newLanguageCode);
  });

  // Check if this is the first time the app is being launched
  final isFirstLaunch = await FirstLaunchService.isFirstLaunch();

  // Check if user is logged in using secure storage
  final hasToken = await SecureStorageService.hasToken();

  // If user is already logged in, load career results and enrolled courses
  if (hasToken) {
    careerGuidanceProvider.loadResultsAfterLogin();

    // Check connectivity and fetch enrolled courses on app startup if online
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      if (isOnline) {
        LoggerService.info('App startup: Online, fetching enrolled courses');
        courseProvider.fetchEnrolledCoursesWithoutContext(forceRefresh: true);
      } else {
        LoggerService.info('App startup: Offline, using cached enrolled courses');
        // Load from cache without forcing refresh
        courseProvider.fetchEnrolledCoursesWithoutContext(forceRefresh: false);
      }
    } catch (e) {
      LoggerService.error('Error checking connectivity on app startup', e);
      // Fallback to loading cached data
      courseProvider.fetchEnrolledCoursesWithoutContext(forceRefresh: false);
    }
  }

  LoggerService.info(
      'Application starting. First launch: $isFirstLaunch, User logged in: $hasToken');

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider<AppSettingsProvider>(
          create: (_) => appSettingsProvider,
        ),
        ChangeNotifierProvider<CourseProvider>(
          create: (_) => courseProvider,
        ),
        ChangeNotifierProvider<UserProvider>(
          create: (_) => userProvider,
        ),
        ChangeNotifierProvider<LibraryProvider>(
          create: (_) => libraryProvider,
        ),
        ChangeNotifierProvider<CareerGuidanceProvider>(
          create: (_) => careerGuidanceProvider,
        ),
      ],
      child: MyApp(
        isFirstLaunch: isFirstLaunch,
        isLoggedIn: hasToken,
      ),
    ),
  );
}

class MyApp extends StatefulWidget {
  final bool isFirstLaunch;
  final bool isLoggedIn;

  const MyApp({
    super.key,
    required this.isFirstLaunch,
    required this.isLoggedIn,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late StreamSubscription<void> _restartSubscription;
  Key _appKey = UniqueKey();

  @override
  void initState() {
    super.initState();
    // Listen for app restart events
    _restartSubscription = restartStream.listen((_) {
      setState(() {
        // Change the key to force a rebuild of the entire app
        _appKey = UniqueKey();
      });
    });
  }

  @override
  void dispose() {
    _restartSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the app settings provider
    final appSettings = Provider.of<AppSettingsProvider>(context);
    final userProvider = Provider.of<UserProvider>(context);

    return MaterialApp(
      key: _appKey,
      navigatorKey: navigatorKey,
      debugShowCheckedModeBanner: false,
      // title: 'Score App',
      title: 'ZABAI App',

      // Theme configuration
      theme: AppTheme.getLightTheme(),
      darkTheme: AppTheme.getDarkTheme(),
      themeMode: appSettings.themeMode,

      // Localization setup
      locale: appSettings.locale,
      supportedLocales: AppConfig.supportedLocales
          .map((locale) => Locale(locale['languageCode']!))
          .toList(),
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // Locale resolution
      localeResolutionCallback: (locale, supportedLocales) {
        // Check if the current device locale is supported
        for (var supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale?.languageCode) {
            return supportedLocale;
          }
        }
        // If the locale of the device is not supported, use the first one
        // from the list (English in this case).
        return Locale('en');
      },

      // Determine the home screen based on first launch and login status
      home: _getHomeScreen(userProvider),
    );
  }

  /// Determine which screen to show based on first launch and login status
  Widget _getHomeScreen(UserProvider userProvider) {
    // If this is the first launch, show language selection screen
    if (widget.isFirstLaunch) {
      return const LanguageSelectionScreen();
    }

    // If user provider is still loading, show appropriate screen based on initial login status
    if (userProvider.isLoading) {
      return widget.isLoggedIn ? const Dashboard() : const LoginPage();
    }

    // Use the user provider's current state to determine the screen
    return userProvider.isLoggedIn ? const Dashboard() : const LoginPage();
  }
}
