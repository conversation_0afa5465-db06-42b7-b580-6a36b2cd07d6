import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Device types
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// Utility class for responsive design
class ResponsiveUtils {
  /// Screen size breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Get the device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// Check if the device is a mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if the device is a tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// Check if the device is a desktop
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }

  /// Get a value based on the device type
  static T getValueForDeviceType<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final deviceType = getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
    }
  }

  /// Get a value based on the screen width
  static T getValueForScreenWidth<T>({
    required BuildContext context,
    required T defaultValue,
    List<MapEntry<double, T>>? breakpoints,
  }) {
    if (breakpoints == null || breakpoints.isEmpty) {
      return defaultValue;
    }

    final double width = MediaQuery.of(context).size.width;
    T result = defaultValue;

    // Sort breakpoints in ascending order
    final sortedBreakpoints = List<MapEntry<double, T>>.from(breakpoints)
      ..sort((a, b) => a.key.compareTo(b.key));

    // Find the appropriate value based on screen width
    for (final breakpoint in sortedBreakpoints) {
      if (width >= breakpoint.key) {
        result = breakpoint.value;
      } else {
        break;
      }
    }

    return result;
  }

  /// Get a responsive padding
  static EdgeInsets getResponsivePadding({
    required BuildContext context,
    EdgeInsets? mobile,
    EdgeInsets? tablet,
    EdgeInsets? desktop,
  }) {
    final deviceType = getDeviceType(context);

    // Default padding values
    final defaultMobile = const EdgeInsets.all(16.0);
    final defaultTablet = const EdgeInsets.all(24.0);
    final defaultDesktop = const EdgeInsets.all(32.0);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? defaultMobile;
      case DeviceType.tablet:
        return tablet ?? defaultTablet;
      case DeviceType.desktop:
        return desktop ?? defaultDesktop;
    }
  }

  /// Get a responsive font size
  static double getResponsiveFontSize({
    required BuildContext context,
    required double baseFontSize,
    double? maxFontSize,
  }) {
    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return baseFontSize;
      case DeviceType.tablet:
        return baseFontSize * 1.2;
      case DeviceType.desktop:
        final calculatedSize = baseFontSize * 1.4;
        return maxFontSize != null ?
          (calculatedSize > maxFontSize ? maxFontSize : calculatedSize) :
          calculatedSize;
    }
  }

  /// Get a responsive grid column count
  static int getResponsiveGridCount({
    required BuildContext context,
    int? mobile,
    int? tablet,
    int? desktop,
  }) {
    final deviceType = getDeviceType(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Default column counts
    final defaultMobile = 2; // Changed from 1 to 2 for mobile
    final defaultTablet = 3; // Changed from 2 to 3 for tablet
    final defaultDesktop = 4;

    // For very small screens (e.g. small phones in portrait), use 1 column
    if (screenWidth < 400) {
      return 2;
    }

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? defaultMobile;
      case DeviceType.tablet:
        return tablet ?? defaultTablet;
      case DeviceType.desktop:
        return desktop ?? defaultDesktop;
    }
  }

  /// Get a responsive child aspect ratio for grid items
  static double getResponsiveChildAspectRatio({
    required BuildContext context,
    double? mobile,
    double? tablet,
    double? desktop,
  }) {
    // Import AppTheme at the top of the file
    // Use the standard aspect ratio from AppTheme as default
    final defaultAspectRatio = AppTheme.cardAspectRatio;

    final deviceType = getDeviceType(context);

    switch (deviceType) {
      case DeviceType.mobile:
        return mobile ?? defaultAspectRatio;
      case DeviceType.tablet:
        return tablet ?? defaultAspectRatio;
      case DeviceType.desktop:
        return desktop ?? defaultAspectRatio;
    }
  }
}
