import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class LmsServer {
  static const mimeTypeFile = "assets/mimetypes.json";

  late final BuildContext ctx;
  late final Map<String, dynamic> mimetypes;
  late final HttpServer server;
  final int port;
  final InternetAddress? address;

  late final List<Directory> rootDirs;

  LmsServer(BuildContext context, List<Directory> rootPaths,
      {this.address, this.port = 26888}) {
    ctx = context;
    rootDirs = rootPaths;
  }

  Future<void> initializeServer() async {
    await _loadMimeDefinitions();
    await _bindServerToPort();
  }

  _loadMimeDefinitions() async {
    String mimeFile =
        await DefaultAssetBundle.of(ctx).loadString(mimeTypeFile);
    mimetypes = jsonDecode(mimeFile);
  }

  _bindServerToPort() async {
    try {
      server =
          await HttpServer.bind(address ?? InternetAddress.loopbackIPv4, port);
    } on SocketException {
      debugPrintStack();
      server = await HttpServer.bind(
          address ?? InternetAddress.loopbackIPv4, port,
          shared: true);
      await server.close(force: true);
      server =
          await HttpServer.bind(address ?? InternetAddress.loopbackIPv4, port);
    }
    server.listen(_onRequest, onError: _onError, onDone: _onDone);
  }

  _onRequest(HttpRequest request) async {
    final pathSegments = request.uri.pathSegments;
    final resourceName = pathSegments[pathSegments.length - 1];

    final hasExt = resourceName.lastIndexOf('.');
    String resourceExt = 'html';

    if (hasExt > -1) {
      resourceExt = resourceName.substring(hasExt + 1);
    }

    final content = mimetypes[resourceExt] as String;
    List<String>? contentType = content.split('/');

    request.response.headers.contentType =
        ContentType(contentType[0], contentType[1]);

    String path = '';
    for (String segment in pathSegments) {
      path += '/$segment';
    }

    for (Directory root in rootDirs) {
      File triedFile = File('${root.path}$path');
      if (kDebugMode) {
        print(triedFile.path);
      }
      if (await triedFile.exists() == true) {
        request.response.statusCode = 200;
        await triedFile.openRead().pipe(request.response);
        await request.response.flush();
        await request.response.close();
        return;
      }
    }

    request.response.statusCode = 404;
    await request.response.close();
    return;
  }

  _onError(Object error, StackTrace trace) async {
    if (kDebugMode) {
      print(error);
      print(trace);
    }
    
  }

  _onDone() async {
    if (kDebugMode) {
      print('On Done Called');
    }
  }

  get listenPort {
    return server.port;
  }

  get serverAddress {
    return server.address;
  }

  stopServer({force = false}) async {
    await server.close(force: force);
  }
}
