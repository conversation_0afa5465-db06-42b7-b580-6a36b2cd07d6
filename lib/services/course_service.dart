import 'dart:convert';
import 'dart:async';
import 'dart:ffi';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:provider/provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/app_config.dart';
import '../models/course_model.dart';
import '../models/library_item_model.dart';
import '../models/user_model.dart';
import '../providers/course_provider.dart';
import '../providers/user_provider.dart';
import '../repositories/course_repository.dart';
import '../repositories/enrolled_course_repository.dart';
import '../services/library_service.dart';
import '../services/logger_service.dart';
import '../services/secure_storage_service.dart';
import 'image_preloader_service.dart';

class CourseService {
  static String get apiUrl =>
      '${AppConfig.apiBaseUrl}${AppConfig.apiGetCourseList}';
  static List<Course>? _cachedCourses;
  static List<EnrolledCourse>? _cachedEnrolledCourses;
  static final CourseRepository _repository = CourseRepository();
  static final EnrolledCourseRepository _enrolledRepository = EnrolledCourseRepository();

  // Fetch courses from API and store in database
  static Future<List<Course>> fetchCourses({bool forceRefresh = false}) async {
    try {
      // Return cached data if available and not forcing refresh
      if (_cachedCourses != null && !forceRefresh) {
        LoggerService.debug(
            'Returning ${_cachedCourses!.length} in-memory cached courses');
        return _cachedCourses!;
      }

      // Try to get courses from database first
      if (!forceRefresh) {
        final dbCourses = await _repository.getAllCourses();
        if (dbCourses.isNotEmpty) {
          LoggerService.debug(
              'Returning ${dbCourses.length} database cached courses');
          _cachedCourses = dbCourses;
          return dbCourses;
        }
      }

      LoggerService.debug('Preparing to fetch courses from API');

      // Courses endpoint doesn't require authentication
      Map<String, String> headers = {
        'Content-Type': 'application/json',
      };

      LoggerService.info('Fetching courses from API');

      // Fetch from API with timeout
      final client = http.Client();
      http.Response response;

      try {
        response = await client
            .get(
          Uri.parse(apiUrl),
          headers: headers,
        )
            .timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            LoggerService.warning('API request timed out');
            client.close();
            throw TimeoutException('API request timed out');
          },
        );
        LoggerService.debug(
            'API response received with status: ${response.statusCode}');
      } catch (e) {
        client.close();
        LoggerService.error('Failed to fetch from API', e);
        throw Exception('Failed to fetch courses: ${e.toString()}');
      } finally {
        client.close();
      }

      if (response.statusCode == 200) {
        LoggerService.debug('Processing API response');

        dynamic decodedData;
        try {
          // Use UTF-8 decoding for proper Bengali and Burmese text support
          final data = utf8.decode(response.bodyBytes);
          decodedData = json.decode(data);
          LoggerService.debug('Response decoded successfully');
        } catch (e) {
          LoggerService.error('Failed to decode response', e);
          throw Exception('Invalid response format');
        }

        List<dynamic> coursesData;

        // Handle both response formats:
        // 1. New format: Direct array [...]
        // 2. Old format: Structured object with status and payload
        if (decodedData is List) {
          // New format: Direct array response
          LoggerService.debug('Processing direct array response format');
          coursesData = decodedData;
        } else if (decodedData is Map<String, dynamic>) {
          // Old format: Structured response
          LoggerService.debug('Processing structured response format');
          final data = decodedData;

          if (data['status'] == 200) {
            coursesData = data['payload']['courses_data'];
          } else {
            LoggerService.warning(
                'API returned non-200 status: ${data['status']}');
            throw Exception('Failed to load courses: ${data['status']}');
          }
        } else {
          LoggerService.error('Unexpected response format: ${decodedData.runtimeType}');
          throw Exception('Invalid response format');
        }

        LoggerService.debug(
            'Found ${coursesData.length} courses in API response');

        // Convert to Course objects
        final List<Course> courses = coursesData
            .map((courseData) => Course.fromJson(courseData))
            .toList();

        // Save to database
        await _repository.insertCourses(courses);
        LoggerService.debug('Saved ${courses.length} courses to database');

        // Cache the data
        _cachedCourses = courses;

        // Preload course thumbnail images in the background
        // We don't await this to avoid blocking the UI
        preloadCourseThumbnailImages(courses);

        return courses;
      } else {
        LoggerService.warning(
            'HTTP request failed with status: ${response.statusCode}');
        throw Exception(
            'Failed to connect to the server: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error fetching courses', e);

      // If we have cached courses, return them even if there was an error
      if (_cachedCourses != null) {
        LoggerService.warning('Returning cached courses after error');
        return _cachedCourses!;
      }

      // Try to get courses from database as a fallback
      final dbCourses = await _repository.getAllCourses();
      if (dbCourses.isNotEmpty) {
        LoggerService.warning('Returning database courses after error');
        _cachedCourses = dbCourses;
        return dbCourses;
      }

      rethrow;
    }
  }

  // Get a course by ID
  static Future<Course?> getCourseById(int id) async {
    try {
      // Check cache first
      if (_cachedCourses != null) {
        final cachedCourse = _cachedCourses!.firstWhere(
            (course) => course.id == id,
            orElse: () => Course(
                id: -1,
                banner: '',
                title: '',
                slug: '',
                description: '',
                modules: []));

        if (cachedCourse.id != -1) {
          return cachedCourse;
        }
      }

      // Try to get from database
      return await _repository.getCourseById(id);
    } catch (e) {
      LoggerService.error('Error getting course by ID', e);
      return null;
    }
  }

  // Clear the cache and optionally the database
  static Future<void> clearCache({bool clearDatabase = false}) async {
    _cachedCourses = null;

    if (clearDatabase) {
      await _repository.deleteAllCourses();
    }
  }

  // Clear enrolled courses cache and optionally the database
  static Future<void> clearEnrolledCoursesCache({bool clearDatabase = false, int? userId}) async {
    _cachedEnrolledCourses = null;

    if (clearDatabase && userId != null) {
      await _enrolledRepository.deleteAllEnrolledCourses(userId);
    }
  }

  // Get authentication headers for API requests
  static Future<Map<String, String>> _getAuthHeaders() async {
    final token = await SecureStorageService.getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Get current user ID from user provider
  static Future<int?> _getCurrentUserId(BuildContext? context) async {
    try {
      if (context != null) {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final userId = userProvider.user?.id;
        LoggerService.debug('Retrieved user ID: $userId from UserProvider');
        return userId;
      }
      LoggerService.warning('Context is null, cannot retrieve user ID');
      return null;
    } catch (e) {
      LoggerService.error('Error getting current user ID', e);
      return null;
    }
  }

  // Check if device is online
  static Future<bool> _isOnline() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      LoggerService.error('Error checking connectivity', e);
      return false;
    }
  }

  // Fetch related library content for a course
  static Future<List<LibraryItem>> fetchRelatedLibraryContent(
      String courseSlug) async {
    try {
      LoggerService.info(
          'Fetching related library content for course: $courseSlug');

      // This endpoint requires authentication
      Map<String, String> headers;
      try {
        headers = await _getAuthHeaders().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            LoggerService.warning('Auth headers request timed out');
            throw TimeoutException('Auth headers request timed out');
          },
        );
        LoggerService.debug(
            'Auth headers obtained successfully for related library content');
      } catch (e) {
        LoggerService.error(
            'Failed to get auth headers for related library content', e);
        return []; // Return empty list instead of throwing to avoid breaking the UI
      }

      final url =
          '${AppConfig.apiBaseUrl}${AppConfig.apiCourseLibraryContent}/$courseSlug';

      // Fetch from API with timeout
      final client = http.Client();
      http.Response response;

      try {
        response = await client
            .get(
          Uri.parse(url),
          headers: headers,
        )
            .timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            LoggerService.warning(
                'API request timed out for related library content');
            client.close();
            throw TimeoutException('API request timed out');
          },
        );
        LoggerService.debug(
            'API response received with status: ${response.statusCode}');
      } catch (e) {
        client.close();
        LoggerService.error(
            'Failed to fetch related library content from API', e);
        return []; // Return empty list instead of throwing to avoid breaking the UI
      } finally {
        client.close();
      }

      if (response.statusCode == 200) {
        Map<String, dynamic> data;
        try {
          // Use UTF-8 decoding for proper Bengali and Burmese text support
          final decodedData = utf8.decode(response.bodyBytes);
          data = json.decode(decodedData);
          LoggerService.debug(
              'Response decoded successfully for related library content');
        } catch (e) {
          LoggerService.error(
              'Failed to decode response for related library content', e);
          return []; // Return empty list instead of throwing to avoid breaking the UI
        }

        if (data['status'] == 200 && data['payload'] != null) {
          final payload = data['payload'];
          final String libraryContentIds = payload['library_contents'] ?? '';

          if (libraryContentIds.isEmpty) {
            LoggerService.info(
                'No related library content found for course: $courseSlug');
            return [];
          }

          // Split the comma-separated IDs
          final List<String> contentIdStrings = libraryContentIds.split(',');
          final List<int> contentIds = [];

          // Safely parse IDs
          for (final idStr in contentIdStrings) {
            try {
              final id = int.parse(idStr.trim());
              contentIds.add(id);
            } catch (e) {
              LoggerService.warning(
                  'Invalid ID format in related library content: $idStr', e);
              // Skip invalid IDs
            }
          }

          LoggerService.info(
              'Found ${contentIds.length} related library items for course: $courseSlug');

          // Fetch the actual library items
          final List<LibraryItem> libraryItems = [];
          for (final id in contentIds) {
            try {
              final item = await LibraryService.getLibraryItemById(id);
              if (item != null) {
                libraryItems.add(item);
              }
            } catch (e) {
              LoggerService.warning(
                  'Failed to fetch library item with ID: $id', e);
              // Continue with other items
            }
          }

          return libraryItems;
        } else {
          LoggerService.warning(
              'Invalid response format for related library content: ${data['status']}');
          return [];
        }
      } else {
        LoggerService.error(
            'Failed to fetch related library content: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      LoggerService.error('Error fetching related library content', e);
      return [];
    }
  }

  // Preload course thumbnail images for faster display
  static Future<void> preloadCourseThumbnailImages(List<Course> courses) async {
    try {
      LoggerService.debug(
          'Preloading ${courses.length} course thumbnail images using ImagePreloaderService');

      // Extract banner URLs from courses
      final bannerUrls = courses
          .where((course) => course.banner.isNotEmpty)
          .map((course) => course.banner)
          .toList();

      // Use the optimized image preloader service
      await ImagePreloaderService.preloadCourseThumbnails(bannerUrls);

      LoggerService.debug('Finished preloading course thumbnail images');
    } catch (e) {
      LoggerService.error('Error preloading course thumbnail images', e);
    }
  }

  static Future<bool> enrollToCourse(BuildContext context, Course course) async {
    try {
      LoggerService.debug('Preparing to enroll in course with API');

      // Get authentication headers
      Map<String, String> headers;
      try {
        headers = await _getAuthHeaders().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            LoggerService.warning('Auth headers request timed out');
            throw TimeoutException('Auth headers request timed out');
          },
        );
        LoggerService.debug('Auth headers obtained successfully for enrolled courses');
      } catch (e) {
        LoggerService.error('Failed to get auth headers for enrolled courses', e);
        throw Exception('Authentication required to fetch enrolled courses');
      }

      final url = '${AppConfig.apiBaseUrl}${AppConfig.apiCourseEnroll}';
      LoggerService.info('Enrolling User to course with API: $url');

      // call API with timeout
      final client = http.Client();
      http.Response response;

      try {
        response = await client
            .post(
          Uri.parse(url),
          headers: headers,
          body: jsonEncode({'course_id': course.id}),
        )
            .timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            LoggerService.warning('API request timed out for enroll to  course');
            client.close();
            throw TimeoutException('API request timed out');
          },
        );
        LoggerService.debug(
            'API response received with status: ${response.statusCode}');
      } catch (e) {
        client.close();
        LoggerService.error('Failed to enroll to course with API', e);
        throw Exception('Failed to enroll to course: ${e.toString()}');
      } finally {
        client.close();
      }

      if (response.statusCode == 201 || response.statusCode == 200) { //response status 201 = Created or 200 = already enrolled
        try {
          LoggerService.debug('Enrollment successful');
          Provider.of<CourseProvider>(context, listen: false).fetchEnrolledCourses(forceRefresh: true, context: context);
        } catch (e) {
          LoggerService.error('Failed to decode enrollment response', e);
          throw Exception('Invalid response format');
        }
        return true;
      } else {
        LoggerService.warning(
            'HTTP request failed with status for enrollment API: ${response.statusCode}');
        throw Exception(
            'Failed to connect to the server: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error enrolling to course', e);
      rethrow;
    }
  }

  static Future<List<EnrolledCourse>> fetchEnrolledCourses({bool forceRefresh = false, BuildContext? context}) async {
    try {
      LoggerService.debug('Preparing to fetch enrolled courses');

      // Return cached data if available and not forcing refresh
      if (_cachedEnrolledCourses != null && !forceRefresh) {
        LoggerService.debug('Returning ${_cachedEnrolledCourses!.length} in-memory cached enrolled courses');
        return _cachedEnrolledCourses!;
      }

      // Get current user ID
      int? userId;
      if (context != null) {
        userId = await _getCurrentUserId(context);
      }

      // Try to get enrolled courses from database first if not forcing refresh
      if (!forceRefresh && userId != null) {
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
        if (dbEnrolledCourses.isNotEmpty) {
          LoggerService.debug('Returning ${dbEnrolledCourses.length} database cached enrolled courses');
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        }
      }

      // Check if device is online
      final isOnline = await _isOnline();
      if (!isOnline) {
        LoggerService.warning('Device is offline, returning cached enrolled courses from database');
        if (userId != null) {
          final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        } else {
          throw Exception('No user logged in and device is offline');
        }
      }

      LoggerService.debug('Fetching enrolled courses from API');

      // Get authentication headers
      Map<String, String> headers;
      try {
        headers = await _getAuthHeaders().timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            LoggerService.warning('Auth headers request timed out');
            throw TimeoutException('Auth headers request timed out');
          },
        );
        LoggerService.debug('Auth headers obtained successfully for enrolled courses');
      } catch (e) {
        LoggerService.error('Failed to get auth headers for enrolled courses', e);
        // If we can't get auth headers but have cached data, return it
        if (userId != null) {
          final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
          if (dbEnrolledCourses.isNotEmpty) {
            LoggerService.warning('Returning cached enrolled courses due to auth error');
            _cachedEnrolledCourses = dbEnrolledCourses;
            return dbEnrolledCourses;
          }
        }
        throw Exception('Authentication required to fetch enrolled courses');
      }

      final url = '${AppConfig.apiBaseUrl}${AppConfig.apiMyCourses}';
      LoggerService.info('Fetching enrolled courses from API: $url');

      // Fetch from API with timeout
      final client = http.Client();
      http.Response response;

      try {
        response = await client
            .get(
          Uri.parse(url),
          headers: headers,
        )
            .timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            LoggerService.warning('API request timed out for enrolled courses');
            client.close();
            throw TimeoutException('API request timed out');
          },
        );
        LoggerService.debug(
            'API response received with status: ${response.statusCode}');
      } catch (e) {
        client.close();
        LoggerService.error('Failed to fetch enrolled courses from API', e);
        // If API fails but we have cached data, return it
        if (userId != null) {
          final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
          if (dbEnrolledCourses.isNotEmpty) {
            LoggerService.warning('Returning cached enrolled courses due to API error');
            _cachedEnrolledCourses = dbEnrolledCourses;
            return dbEnrolledCourses;
          }
        }
        throw Exception('Failed to fetch enrolled courses: ${e.toString()}');
      } finally {
        client.close();
      }

      if (response.statusCode == 200) {
        LoggerService.debug('Processing enrolled courses API response');

        List<dynamic> data;
        try {
          // Use UTF-8 decoding for proper Bengali and Burmese text support
          final decodedData = utf8.decode(response.bodyBytes);
          data = json.decode(decodedData);
          LoggerService.debug('Enrolled courses response decoded successfully');
        } catch (e) {
          LoggerService.error('Failed to decode enrolled courses response', e);
          throw Exception('Invalid response format');
        }

        LoggerService.debug('Found ${data.length} enrolled courses in API response');

        // Convert to EnrolledCourse objects
        final List<EnrolledCourse> enrolledCourses = data
            .map((courseData) => EnrolledCourse.fromJson(courseData))
            .toList();

        LoggerService.debug('Successfully parsed ${enrolledCourses.length} enrolled courses');

        // Save to database if user is available
        if (userId != null) {
          try {
            LoggerService.debug('Attempting to save ${enrolledCourses.length} enrolled courses to database for user $userId');
            await _enrolledRepository.insertEnrolledCourses(enrolledCourses, userId);
            LoggerService.debug('Successfully saved ${enrolledCourses.length} enrolled courses to database for user $userId');
          } catch (e) {
            LoggerService.error('Failed to save enrolled courses to database for user $userId', e);
            // Continue even if database save fails
          }
        } else {
          LoggerService.warning('User ID is null, cannot save enrolled courses to database');
        }

        // Cache the data
        _cachedEnrolledCourses = enrolledCourses;

        return enrolledCourses;
      } else {
        LoggerService.warning(
            'HTTP request failed with status for enrolled courses: ${response.statusCode}');
        // If API returns error but we have cached data, return it
        if (userId != null) {
          final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
          if (dbEnrolledCourses.isNotEmpty) {
            LoggerService.warning('Returning cached enrolled courses due to API error status');
            _cachedEnrolledCourses = dbEnrolledCourses;
            return dbEnrolledCourses;
          }
        }
        throw Exception(
            'Failed to connect to the server: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error fetching enrolled courses', e);

      // Final fallback to cached data
      int? finalUserId;
      if (context != null) {
        finalUserId = await _getCurrentUserId(context);
      }

      if (finalUserId != null) {
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(finalUserId);
        if (dbEnrolledCourses.isNotEmpty) {
          LoggerService.warning('Returning cached enrolled courses as final fallback');
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        }
      }

      rethrow;
    }
  }

  static Future<List<Course>> getEnrolledCoursesWithDetails({bool forceRefresh = false, BuildContext? context}) async {
    try {
      LoggerService.debug('Getting enrolled courses with full details');

      // First, fetch the enrolled courses to get the IDs
      final enrolledCourses = await fetchEnrolledCourses(forceRefresh: forceRefresh, context: context);

      if (enrolledCourses.isEmpty) {
        LoggerService.debug('No enrolled courses found');
        return [];
      }

      // Get the list of enrolled course IDs
      final enrolledCourseIds = enrolledCourses.map((course) => course.id).toSet();
      LoggerService.debug('Found enrolled course IDs: $enrolledCourseIds');

      // Fetch all available courses
      final allCourses = await fetchCourses(forceRefresh: forceRefresh);

      // Filter courses to only include enrolled ones
      final enrolledCoursesWithDetails = allCourses
          .where((course) => enrolledCourseIds.contains(course.id))
          .toList();

      LoggerService.debug(
          'Found ${enrolledCoursesWithDetails.length} enrolled courses with full details');

      return enrolledCoursesWithDetails;
    } catch (e) {
      LoggerService.error('Error getting enrolled courses with details', e);
      rethrow;
    }
  }

  /// Fetch enrolled courses directly with user ID (for use without context)
  static Future<List<EnrolledCourse>> fetchEnrolledCoursesDirectly({bool forceRefresh = false, required int userId}) async {
    try {
      LoggerService.debug('Fetching enrolled courses directly for user $userId');

      // Return cached data if available and not forcing refresh
      if (_cachedEnrolledCourses != null && !forceRefresh) {
        LoggerService.debug('Returning ${_cachedEnrolledCourses!.length} in-memory cached enrolled courses');
        return _cachedEnrolledCourses!;
      }

      // Try to get enrolled courses from database first if not forcing refresh
      if (!forceRefresh) {
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
        if (dbEnrolledCourses.isNotEmpty) {
          LoggerService.debug('Returning ${dbEnrolledCourses.length} database cached enrolled courses');
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        }
      }

      // Check if device is online
      final isOnline = await _isOnline();
      if (!isOnline) {
        LoggerService.warning('Device is offline, returning cached enrolled courses from database');
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
        _cachedEnrolledCourses = dbEnrolledCourses;
        return dbEnrolledCourses;
      }

      LoggerService.debug('Fetching enrolled courses from API');

      // Get authentication headers
      Map<String, String> headers;
      try {
        headers = await _getAuthHeaders().timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            LoggerService.warning('Auth headers request timed out');
            throw TimeoutException('Auth headers request timed out');
          },
        );
        LoggerService.debug('Auth headers obtained successfully for enrolled courses');
      } catch (e) {
        LoggerService.error('Failed to get auth headers for enrolled courses', e);
        // If we can't get auth headers but have cached data, return it
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
        if (dbEnrolledCourses.isNotEmpty) {
          LoggerService.warning('Returning cached enrolled courses due to auth error');
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        }
        throw Exception('Authentication required to fetch enrolled courses');
      }

      final url = '${AppConfig.apiBaseUrl}${AppConfig.apiMyCourses}';
      LoggerService.info('Fetching enrolled courses from API: $url');

      // Fetch from API with timeout
      final client = http.Client();
      http.Response response;

      try {
        response = await client
            .get(
          Uri.parse(url),
          headers: headers,
        )
            .timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            LoggerService.warning('API request timed out for enrolled courses');
            client.close();
            throw TimeoutException('API request timed out');
          },
        );
        LoggerService.debug(
            'API response received with status: ${response.statusCode}');
      } catch (e) {
        client.close();
        LoggerService.error('Failed to fetch enrolled courses from API', e);
        // If API fails but we have cached data, return it
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
        if (dbEnrolledCourses.isNotEmpty) {
          LoggerService.warning('Returning cached enrolled courses due to API error');
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        }
        throw Exception('Failed to fetch enrolled courses: ${e.toString()}');
      } finally {
        client.close();
      }

      if (response.statusCode == 200) {
        LoggerService.debug('Processing enrolled courses API response');

        List<dynamic> data;
        try {
          // Use UTF-8 decoding for proper Bengali and Burmese text support
          final decodedData = utf8.decode(response.bodyBytes);
          data = json.decode(decodedData);
          LoggerService.debug('Enrolled courses response decoded successfully');
        } catch (e) {
          LoggerService.error('Failed to decode enrolled courses response', e);
          throw Exception('Invalid response format');
        }

        LoggerService.debug('Found ${data.length} enrolled courses in API response');

        // Convert to EnrolledCourse objects
        final List<EnrolledCourse> enrolledCourses = data
            .map((courseData) => EnrolledCourse.fromJson(courseData))
            .toList();

        LoggerService.debug('Successfully parsed ${enrolledCourses.length} enrolled courses');

        // Save to database
        try {
          LoggerService.debug('Attempting to save ${enrolledCourses.length} enrolled courses to database for user $userId');
          await _enrolledRepository.insertEnrolledCourses(enrolledCourses, userId);
          LoggerService.debug('Successfully saved ${enrolledCourses.length} enrolled courses to database for user $userId');
        } catch (e) {
          LoggerService.error('Failed to save enrolled courses to database for user $userId', e);
          // Continue even if database save fails
        }

        // Cache the data
        _cachedEnrolledCourses = enrolledCourses;

        return enrolledCourses;
      } else {
        LoggerService.warning(
            'HTTP request failed with status for enrolled courses: ${response.statusCode}');
        // If API returns error but we have cached data, return it
        final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
        if (dbEnrolledCourses.isNotEmpty) {
          LoggerService.warning('Returning cached enrolled courses due to API error status');
          _cachedEnrolledCourses = dbEnrolledCourses;
          return dbEnrolledCourses;
        }
        throw Exception(
            'Failed to connect to the server: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error fetching enrolled courses directly', e);

      // Final fallback to cached data
      final dbEnrolledCourses = await _enrolledRepository.getEnrolledCourses(userId);
      if (dbEnrolledCourses.isNotEmpty) {
        LoggerService.warning('Returning cached enrolled courses as final fallback');
        _cachedEnrolledCourses = dbEnrolledCourses;
        return dbEnrolledCourses;
      }

      rethrow;
    }
  }

  /// Get enrolled courses with details directly with user ID (for use without context)
  static Future<List<Course>> getEnrolledCoursesWithDetailsDirectly({bool forceRefresh = false, required int userId}) async {
    try {
      LoggerService.debug('Getting enrolled courses with full details directly for user $userId');

      // First, fetch the enrolled courses to get the IDs
      final enrolledCourses = await fetchEnrolledCoursesDirectly(forceRefresh: forceRefresh, userId: userId);

      if (enrolledCourses.isEmpty) {
        LoggerService.debug('No enrolled courses found');
        return [];
      }

      // Get the list of enrolled course IDs
      final enrolledCourseIds = enrolledCourses.map((course) => course.id).toSet();
      LoggerService.debug('Found enrolled course IDs: $enrolledCourseIds');

      // Fetch all available courses
      final allCourses = await fetchCourses(forceRefresh: forceRefresh);

      // Filter courses to only include enrolled ones
      final enrolledCoursesWithDetails = allCourses
          .where((course) => enrolledCourseIds.contains(course.id))
          .toList();

      LoggerService.debug(
          'Found ${enrolledCoursesWithDetails.length} enrolled courses with full details');

      return enrolledCoursesWithDetails;
    } catch (e) {
      LoggerService.error('Error getting enrolled courses with details directly', e);
      rethrow;
    }
  }

}
