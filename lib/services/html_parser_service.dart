import 'dart:convert';
import 'dart:typed_data';
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart' as dom;

/// Data model for parsed HTML
class ParsedHtmlContent {
  final List<String> paragraphs;
  final List<String> listItems;
  final List<LinkItem> links;
  final List<Base64ImageItem> base64Images;
  final List<HtmlElement> orderedElements; // New: maintains original order

  ParsedHtmlContent({
    required this.paragraphs,
    required this.listItems,
    required this.links,
    required this.base64Images,
    required this.orderedElements,
  });
}

/// Base class for HTML elements to maintain order
abstract class HtmlElement {
  final String type;
  HtmlElement(this.type);
}

/// Paragraph element
class ParagraphElement extends HtmlElement {
  final String text;
  ParagraphElement(this.text) : super('paragraph');
}

/// List item element
class ListItemElement extends HtmlElement {
  final String text;
  ListItemElement(this.text) : super('listItem');
}

/// Link element
class LinkElement extends HtmlElement {
  final LinkItem link;
  LinkElement(this.link) : super('link');
}

/// Image element
class ImageElement extends HtmlElement {
  final Base64ImageItem image;
  ImageElement(this.image) : super('image');
}

/// Horizontal rule element
class HrElement extends HtmlElement {
  HrElement() : super('hr');
}

/// Model for a link
class LinkItem {
  final String text;
  final String href;

  LinkItem({required this.text, required this.href});
}

/// Model for a base64 image
class Base64ImageItem {
  final Uint8List bytes;
  final String mimeType;

  Base64ImageItem({required this.bytes, required this.mimeType});
}

/// Service class to parse HTML
class HtmlParserService {
  ParsedHtmlContent parse(String htmlString) {
    final dom.Document document = html_parser.parse(htmlString);

    final paragraphs = document
        .getElementsByTagName('p')
        .map((e) => e.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();

    final listItems = document
        .getElementsByTagName('li')
        .map((li) => li.text.trim())
        .where((text) => text.isNotEmpty)
        .toList();

    final links = document.getElementsByTagName('a').map((a) {
      return LinkItem(
        text: a.text.trim(),
        href: a.attributes['href'] ?? '',
      );
    }).toList();

    final base64Images = document
        .getElementsByTagName('img')
        .map((img) {
          final src = img.attributes['src'] ?? '';
          if (src.startsWith('data:image')) {
            final regex = RegExp(r'data:(image/\w+);base64,(.*)');
            final match = regex.firstMatch(src);
            if (match != null) {
              final mimeType = match.group(1)!;
              final base64Data = match.group(2)!;
              final bytes = base64Decode(base64Data);
              return Base64ImageItem(bytes: bytes, mimeType: mimeType);
            }
          }
          return null;
        })
        .whereType<Base64ImageItem>()
        .toList();

    // NEW: Parse elements in document order
    final orderedElements = <HtmlElement>[];
    final bodyElement = document.body;

    if (bodyElement != null) {
      _parseElementsInOrder(bodyElement, orderedElements);
    }

    return ParsedHtmlContent(
      paragraphs: paragraphs,
      listItems: listItems,
      links: links,
      base64Images: base64Images,
      orderedElements: orderedElements,
    );
  }

  /// Recursively parse elements maintaining document order
  void _parseElementsInOrder(
      dom.Element element, List<HtmlElement> orderedElements) {
    for (final child in element.children) {
      switch (child.localName?.toLowerCase()) {
        case 'p':
          // Check if paragraph contains images first
          final hasImages = child.getElementsByTagName('img').isNotEmpty;

          if (hasImages) {
            // Handle images within paragraphs
            for (final img in child.getElementsByTagName('img')) {
              final src = img.attributes['src'] ?? '';
              if (src.startsWith('data:image')) {
                final regex = RegExp(r'data:(image/\w+);base64,(.*)');
                final match = regex.firstMatch(src);
                if (match != null) {
                  final mimeType = match.group(1)!;
                  final base64Data = match.group(2)!;
                  final bytes = base64Decode(base64Data);
                  orderedElements.add(ImageElement(
                      Base64ImageItem(bytes: bytes, mimeType: mimeType)));
                }
              }
            }
          }

          // Also check for text content in the paragraph
          final text = child.text.trim();
          if (text.isNotEmpty) {
            // Regular paragraph (may contain links)
            orderedElements.add(ParagraphElement(text));
          }
          break;
        case 'li':
          final text = child.text.trim();
          if (text.isNotEmpty) {
            orderedElements.add(ListItemElement(text));
          }
          break;
        case 'hr':
          orderedElements.add(HrElement());
          break;
        case 'ul':
        case 'ol':
          // Process list items within lists
          _parseElementsInOrder(child, orderedElements);
          break;
        default:
          // Recursively process other elements
          _parseElementsInOrder(child, orderedElements);
          break;
      }
    }
  }
}
