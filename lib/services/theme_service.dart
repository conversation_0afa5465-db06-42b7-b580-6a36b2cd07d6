import 'package:shared_preferences/shared_preferences.dart';
import '../services/logger_service.dart';

/// Service to manage application theme settings
class ThemeService {
  static const String _darkModeKey = 'dark_mode';
  
  /// Check if dark mode is enabled
  static Future<bool> isDarkMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_darkModeKey) ?? false;
    } catch (e) {
      LoggerService.error('Error getting dark mode setting', e);
      return false;
    }
  }
  
  /// Set dark mode preference
  static Future<bool> setDarkMode(bool isDarkMode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_darkModeKey, isDarkMode);
      LoggerService.info('Dark mode set to: $isDarkMode');
      return true;
    } catch (e) {
      LoggerService.error('Error setting dark mode', e);
      return false;
    }
  }
}
