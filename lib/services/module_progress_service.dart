import 'dart:io';
import '../models/module_progress_model.dart';
import '../repositories/module_progress_repository.dart';
import '../services/api_service.dart';
import '../services/logger_service.dart';

/// Service for managing module progress (for non-SCORM content like PDF, audio, video)
class ModuleProgressService {
  static final ModuleProgressRepository _repository = ModuleProgressRepository();

  /// Check if device is online
  static Future<bool> _isOnline() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Save module progress locally
  static Future<bool> saveProgress({
    required int userId,
    required int moduleId,
    required String contentType,
    required bool completionStatus,
    Map<String, dynamic>? progressData,
  }) async {
    try {
      LoggerService.debug('Saving module progress for user $userId, module $moduleId, type $contentType');

      // Get existing progress if any
      final existingProgress = await _repository.getProgress(userId, moduleId);

      ModuleProgress progress;
      if (existingProgress != null) {
        // Update existing progress
        progress = existingProgress.copyWith(
          completionStatus: completionStatus,
          progressData: progressData ?? existingProgress.progressData,
          lastAccessed: DateTime.now(),
          syncStatus: false, // Mark as needing sync
          updatedAt: DateTime.now(),
        );
      } else {
        // Create new progress
        progress = ModuleProgress(
          userId: userId,
          moduleId: moduleId,
          contentType: contentType,
          completionStatus: completionStatus,
          progressData: progressData,
          lastAccessed: DateTime.now(),
          syncStatus: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      final result = await _repository.saveProgress(progress);

      if (result > 0) {
        LoggerService.debug('Module progress saved locally for user $userId, module $moduleId');

        // Try to sync if online
        await _trySyncProgress(userId, moduleId);

        return true;
      }

      return false;
    } catch (e) {
      LoggerService.error('Error saving module progress', e);
      return false;
    }
  }

  /// Mark module as completed
  static Future<bool> markAsCompleted({
    required int userId,
    required int moduleId,
    required String contentType,
    Map<String, dynamic>? progressData,
  }) async {
    return await saveProgress(
      userId: userId,
      moduleId: moduleId,
      contentType: contentType,
      completionStatus: true,
      progressData: progressData,
    );
  }

  /// Mark module as accessed (but not necessarily completed)
  static Future<bool> markAsAccessed({
    required int userId,
    required int moduleId,
    required String contentType,
    Map<String, dynamic>? progressData,
  }) async {
    try {
      final existingProgress = await _repository.getProgress(userId, moduleId);
      
      // Only update if not already completed
      if (existingProgress?.completionStatus != true) {
        return await saveProgress(
          userId: userId,
          moduleId: moduleId,
          contentType: contentType,
          completionStatus: false,
          progressData: progressData,
        );
      }
      
      // Just update last accessed time if already completed
      await _repository.updateLastAccessed(userId, moduleId);
      return true;
    } catch (e) {
      LoggerService.error('Error marking module as accessed', e);
      return false;
    }
  }

  /// Get module progress
  static Future<ModuleProgress?> getProgress(int userId, int moduleId) async {
    try {
      return await _repository.getProgress(userId, moduleId);
    } catch (e) {
      LoggerService.error('Error getting module progress', e);
      return null;
    }
  }

  /// Get all progress for a user
  static Future<List<ModuleProgress>> getUserProgress(int userId) async {
    try {
      return await _repository.getUserProgress(userId);
    } catch (e) {
      LoggerService.error('Error getting user progress', e);
      return [];
    }
  }

  /// Try to sync specific progress if online
  static Future<void> _trySyncProgress(int userId, int moduleId) async {
    try {
      if (await _isOnline()) {
        final progress = await _repository.getProgress(userId, moduleId);
        if (progress != null && progress.needsSync) {
          await _syncSingleProgress(progress);
        }
      }
    } catch (e) {
      LoggerService.error('Error trying to sync progress', e);
    }
  }

  /// Sync all unsynced progress to server
  static Future<int> syncAllProgress() async {
    try {
      if (!await _isOnline()) {
        LoggerService.debug('Device is offline, skipping module progress sync');
        return 0;
      }

      final unsyncedProgress = await _repository.getUnsyncedProgress();
      
      if (unsyncedProgress.isEmpty) {
        LoggerService.debug('No unsynced module progress found');
        return 0;
      }

      int syncedCount = 0;
      
      for (final progress in unsyncedProgress) {
        try {
          final success = await _syncSingleProgress(progress);
          if (success) {
            syncedCount++;
          }
        } catch (e) {
          LoggerService.error('Error syncing progress for module ${progress.moduleId}', e);
        }
      }

      LoggerService.debug('Synced $syncedCount out of ${unsyncedProgress.length} module progress records');
      return syncedCount;
    } catch (e) {
      LoggerService.error('Error syncing all module progress', e);
      return 0;
    }
  }

  /// Sync single progress record to server
  static Future<bool> _syncSingleProgress(ModuleProgress progress) async {
    try {
      // Prepare data for API
      final syncData = {
        'module_id': progress.moduleId.toString(),
        'state': progress.progressData ?? {},
        'completion_status': progress.completionStatus ? 1 : 0,
      };

      final response = await ApiService.syncModuleProgress(syncData);
      
      if (response['status'] == 200) {
        // Mark as synced
        await _repository.updateSyncStatus(progress.id!, true);
        LoggerService.debug('Successfully synced module progress for module ${progress.moduleId}');
        return true;
      } else {
        // Increment sync attempts
        final updatedProgress = progress.incrementSyncAttempts();
        await _repository.saveProgress(updatedProgress);
        LoggerService.warning('Failed to sync module progress: ${response['message']}');
        return false;
      }
    } catch (e) {
      LoggerService.error('Error syncing single progress', e);
      // Increment sync attempts on error
      try {
        final updatedProgress = progress.incrementSyncAttempts();
        await _repository.saveProgress(updatedProgress);
      } catch (updateError) {
        LoggerService.error('Error updating sync attempts', updateError);
      }
      return false;
    }
  }

  /// Get sync statistics
  static Future<Map<String, int>> getSyncStats(int userId) async {
    try {
      final allProgress = await _repository.getUserProgress(userId);
      final unsyncedProgress = await _repository.getUnsyncedProgress();
      final userUnsyncedCount = unsyncedProgress.where((p) => p.userId == userId).length;
      
      return {
        'total': allProgress.length,
        'synced': allProgress.length - userUnsyncedCount,
        'unsynced': userUnsyncedCount,
        'completed': allProgress.where((p) => p.completionStatus).length,
      };
    } catch (e) {
      LoggerService.error('Error getting sync stats', e);
      return {
        'total': 0,
        'synced': 0,
        'unsynced': 0,
        'completed': 0,
      };
    }
  }

  /// Clear all progress for a user (useful for logout)
  static Future<void> clearUserProgress(int userId) async {
    try {
      await _repository.deleteUserProgress(userId);
      LoggerService.debug('Cleared all module progress for user $userId');
    } catch (e) {
      LoggerService.error('Error clearing user module progress', e);
    }
  }

  /// Force sync specific module progress
  static Future<bool> forceSyncModule(int userId, int moduleId) async {
    try {
      final progress = await _repository.getProgress(userId, moduleId);
      if (progress != null) {
        return await _syncSingleProgress(progress);
      }
      return false;
    } catch (e) {
      LoggerService.error('Error force syncing module progress', e);
      return false;
    }
  }

  /// Get progress by content type
  static Future<List<ModuleProgress>> getProgressByContentType(int userId, String contentType) async {
    try {
      return await _repository.getProgressByContentType(userId, contentType);
    } catch (e) {
      LoggerService.error('Error getting progress by content type', e);
      return [];
    }
  }

  /// Get completed modules count
  static Future<int> getCompletedModulesCount(int userId) async {
    try {
      return await _repository.getCompletedModulesCount(userId);
    } catch (e) {
      LoggerService.error('Error getting completed modules count', e);
      return 0;
    }
  }
}
