import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../config/app_config.dart';
import 'logger_service.dart';

/// Service to preload images in the background for better UI performance
class ImagePreloaderService {
  static final Map<String, bool> _preloadedImages = {};
  static final Map<String, Future<void>> _preloadingFutures = {};

  /// Check if an image is already preloaded
  static bool isImagePreloaded(String imageUrl) {
    if (imageUrl.isEmpty) return false;

    final fullUrl = _getFullImageUrl(imageUrl);
    return _preloadedImages[fullUrl] == true;
  }

  /// Preload a single image in the background
  static Future<void> preloadImage(String imageUrl,
      {int? width, int? height}) async {
    if (imageUrl.isEmpty) return;

    final fullUrl = _getFullImageUrl(imageUrl);
    final optimizedUrl =
        _getOptimizedUrl(fullUrl, width: width, height: height);

    // If already preloaded or currently preloading, return
    if (_preloadedImages[optimizedUrl] == true) return;
    if (_preloadingFutures.containsKey(optimizedUrl)) {
      return _preloadingFutures[optimizedUrl];
    }

    // Start preloading
    final future = _doPreloadImage(optimizedUrl);
    _preloadingFutures[optimizedUrl] = future;

    try {
      await future;
      _preloadedImages[optimizedUrl] = true;
      LoggerService.debug('Successfully preloaded image: $optimizedUrl');
    } catch (e) {
      LoggerService.warning('Failed to preload image: $optimizedUrl', e);
      _preloadedImages[optimizedUrl] = false;
    } finally {
      _preloadingFutures.remove(optimizedUrl);
    }
  }

  /// Preload multiple images in batches for better performance
  static Future<void> preloadImages(
    List<String> imageUrls, {
    int batchSize = 5,
    int? width,
    int? height,
  }) async {
    if (imageUrls.isEmpty) return;

    // Filter out empty URLs and already preloaded images
    final urlsToPreload = imageUrls
        .where((url) => url.isNotEmpty && !isImagePreloaded(url))
        .toList();

    if (urlsToPreload.isEmpty) return;

    LoggerService.info(
        'Preloading ${urlsToPreload.length} images in batches of $batchSize');

    // Process in batches to avoid overwhelming the system
    for (int i = 0; i < urlsToPreload.length; i += batchSize) {
      final batch = urlsToPreload.skip(i).take(batchSize).toList();

      // Preload batch in parallel
      await Future.wait(
        batch.map((url) => preloadImage(url, width: width, height: height)),
        eagerError: false, // Continue even if some images fail
      );

      // Small delay between batches to prevent UI blocking
      if (i + batchSize < urlsToPreload.length) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }

    LoggerService.info('Completed preloading images');
  }

  /// Preload course thumbnails
  static Future<void> preloadCourseThumbnails(
      List<String> thumbnailUrls) async {
    await preloadImages(
      thumbnailUrls,
      batchSize: 3,
      width: 300,
      height: 200,
    );
  }

  /// Preload library item thumbnails
  static Future<void> preloadLibraryThumbnails(
      List<String> thumbnailUrls) async {
    await preloadImages(
      thumbnailUrls,
      batchSize: 4,
      width: 300,
      height: 300,
    );
  }

  /// Clear preloaded image cache
  static void clearPreloadedCache() {
    _preloadedImages.clear();
    _preloadingFutures.clear();
    LoggerService.info('Cleared preloaded image cache');
  }

  /// Get memory usage statistics
  static Map<String, dynamic> getMemoryStats() {
    return {
      'preloaded_count': _preloadedImages.length,
      'currently_preloading': _preloadingFutures.length,
      'successful_preloads':
          _preloadedImages.values.where((v) => v == true).length,
      'failed_preloads':
          _preloadedImages.values.where((v) => v == false).length,
    };
  }

  // Private helper methods

  static String _getFullImageUrl(String imageUrl) {
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    return '${AppConfig.apiBaseUrl}$imageUrl';
  }

  static String _getOptimizedUrl(String fullUrl, {int? width, int? height}) {
    if (width == null && height == null) return fullUrl;

    final uri = Uri.parse(fullUrl);
    final queryParams = Map<String, String>.from(uri.queryParameters);

    if (width != null) queryParams['width'] = width.toString();
    if (height != null) queryParams['height'] = height.toString();

    return uri.replace(queryParameters: queryParams).toString();
  }

  static Future<void> _doPreloadImage(String imageUrl) async {
    try {
      // Use CachedNetworkImageProvider to preload the image
      final provider = CachedNetworkImageProvider(imageUrl);

      // Create a completer to wait for the image to load
      final completer = Completer<void>();

      // Force the image to be loaded and cached
      final imageStream = provider.resolve(const ImageConfiguration());

      // Listen for the image to be loaded
      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo image, bool synchronousCall) {
          // Image loaded successfully
          imageStream.removeListener(listener);
          if (!completer.isCompleted) {
            completer.complete();
          }
        },
        onError: (dynamic exception, StackTrace? stackTrace) {
          // Image failed to load
          imageStream.removeListener(listener);
          if (!completer.isCompleted) {
            completer.completeError(exception, stackTrace);
          }
        },
      );

      imageStream.addListener(listener);

      // Wait for the image to be loaded or fail
      await completer.future;
    } catch (e) {
      // Re-throw to be handled by the caller
      throw Exception('Failed to preload image: $e');
    }
  }
}

/// Extension to easily preload images from lists of objects
extension ImagePreloaderExtensions on List {
  /// Preload images from a list of objects with thumbnail URLs
  Future<void> preloadThumbnails({
    required String Function(dynamic item) getThumbnailUrl,
    int batchSize = 5,
    int? width,
    int? height,
  }) async {
    final urls = map(getThumbnailUrl).where((url) => url.isNotEmpty).toList();
    await ImagePreloaderService.preloadImages(
      urls,
      batchSize: batchSize,
      width: width,
      height: height,
    );
  }
}
