import 'dart:io';
import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../services/logger_service.dart';
import '../services/secure_storage_service.dart';

/// Service for handling image uploads and image picking functionality
class ImageUploadService {
  static final ImagePicker _picker = ImagePicker();

  /// Simple gallery picker (for testing)
  static Future<File?> pickFromGallery() async {
    try {
      LoggerService.info('Attempting to pick image from gallery');

      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        LoggerService.info('Image picked successfully: ${pickedFile.path}');
        final File file = File(pickedFile.path);
        if (await file.exists()) {
          return file;
        } else {
          LoggerService.warning(
              'Picked file does not exist: ${pickedFile.path}');
        }
      } else {
        LoggerService.info('No image was selected');
      }
    } catch (e) {
      LoggerService.error('Error picking image from gallery: $e', e);
    }
    return null;
  }

  /// Show image source selection dialog (camera or gallery)
  static Future<File?> pickImage(BuildContext context) async {
    try {
      final ImageSource? source = await showDialog<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select Image Source'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Camera'),
                  onTap: () => Navigator.of(context).pop(ImageSource.camera),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                ),
              ],
            ),
          );
        },
      );

      if (source == null) return null;

      // Add a small delay to ensure the dialog is fully closed
      await Future.delayed(const Duration(milliseconds: 300));

      LoggerService.info('Attempting to pick image from ${source.name}');

      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        LoggerService.info('Image picked successfully: ${pickedFile.path}');
        final File file = File(pickedFile.path);
        // Verify the file exists and is readable
        if (await file.exists()) {
          return file;
        } else {
          LoggerService.warning(
              'Picked file does not exist: ${pickedFile.path}');
        }
      } else {
        LoggerService.info('No image was selected');
      }
    } catch (e) {
      LoggerService.error('Error picking image: $e', e);
      // Show user-friendly error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    return null;
  }

  /// Simple test method - just try gallery picker
  static Future<File?> testGalleryPicker() async {
    try {
      LoggerService.info('Testing gallery picker...');
      return await pickFromGallery();
    } catch (e) {
      LoggerService.error('Gallery picker test failed: $e', e);
      return null;
    }
  }

  /// Combined image picker that tries different methods
  static Future<File?> pickImageSafely(BuildContext context) async {
    // First try the simple gallery picker (no dialog)
    File? imageFile = await pickFromGallery();

    // If that fails, try the dialog-based picker
    if (imageFile == null && context.mounted) {
      LoggerService.info('Simple gallery picker failed, trying dialog picker');
      imageFile = await pickImage(context);
    }

    return imageFile;
  }

  /// Upload avatar image to the server (avatar only)
  static Future<Map<String, dynamic>> uploadAvatar(File imageFile) async {
    try {
      LoggerService.info('Uploading avatar image only');
      LoggerService.info('Avatar file: ${imageFile.path}');

      final token = await SecureStorageService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Check file exists
      if (!await imageFile.exists()) {
        throw Exception('Avatar file does not exist: ${imageFile.path}');
      }

      final fileSize = await imageFile.length();
      LoggerService.info('Avatar file size: $fileSize bytes');

      // Create Dio instance with logging
      final dio = Dio();

      // Add interceptor for debugging
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: true,
        error: true,
      ));

      // Get file extension
      final extension = imageFile.path.toLowerCase().split('.').last;

      // Prepare form data with ONLY avatar file
      final formData = FormData.fromMap({
        'avatar': await MultipartFile.fromFile(
          imageFile.path,
          filename: 'avatar.$extension',
        ),
      });

      LoggerService.info(
          'Form data fields: ${formData.fields.map((e) => e.key).toList()}');
      LoggerService.info(
          'Form data files: ${formData.files.map((e) => e.key).toList()}');
      LoggerService.info(
          'Making request to: ${AppConfig.apiBaseUrl}${AppConfig.apiUpdateProfile}');

      // Make the request
      final response = await dio.put(
        '${AppConfig.apiBaseUrl}${AppConfig.apiUpdateProfile}',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            // Don't set Content-Type manually for multipart
          },
          validateStatus: (status) {
            // Accept all status codes to handle them manually
            return status != null && status < 600;
          },
        ),
      );

      LoggerService.info('Avatar upload response: ${response.statusCode}');
      LoggerService.info('Response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic>) {
          // API now returns profile data directly, just return it as-is
          // The calling code will check for 'id' field to determine success
          return responseData;
        } else {
          return {'status': 200, 'message': 'Avatar updated successfully'};
        }
      } else {
        LoggerService.error(
            'Server error: ${response.statusCode} - ${response.data}');
        return {
          'status': response.statusCode,
          'error': 'Server error: ${response.statusCode} - ${response.data}'
        };
      }
    } catch (e) {
      LoggerService.error('Avatar upload error: $e', e);
      return {
        'status': 500,
        'error': 'Failed to upload avatar: ${e.toString()}'
      };
    }
  }

  /// Update profile with both text data and optional avatar
  static Future<Map<String, dynamic>> updateProfileWithAvatar({
    required Map<String, dynamic> profileData,
    File? avatarFile,
  }) async {
    try {
      LoggerService.info('Updating profile with avatar');
      LoggerService.info('Profile data: $profileData');
      LoggerService.info('Avatar file: ${avatarFile?.path}');

      final token = await SecureStorageService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Create Dio instance with detailed logging
      final dio = Dio();

      // Add interceptor for debugging
      dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: true,
        error: true,
      ));

      // Prepare form data
      final Map<String, dynamic> formFields = Map.from(profileData);

      // Add avatar if provided
      if (avatarFile != null) {
        // Check file exists and get file info
        if (!await avatarFile.exists()) {
          throw Exception('Avatar file does not exist: ${avatarFile.path}');
        }

        final fileSize = await avatarFile.length();
        LoggerService.info('Avatar file size: $fileSize bytes');

        // Get file extension for filename
        final extension = avatarFile.path.toLowerCase().split('.').last;

        formFields['avatar'] = await MultipartFile.fromFile(
          avatarFile.path,
          filename: 'avatar.$extension',
        );
      }

      final formData = FormData.fromMap(formFields);

      LoggerService.info(
          'Form data fields: ${formData.fields.map((e) => e.key).toList()}');
      LoggerService.info(
          'Form data files: ${formData.files.map((e) => e.key).toList()}');

      // Make the request
      final response = await dio.put(
        '${AppConfig.apiBaseUrl}${AppConfig.apiUpdateProfile}',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            // Don't set Content-Type manually for multipart, let Dio handle it
          },
          validateStatus: (status) {
            // Accept all status codes to handle them manually
            return status != null && status < 600;
          },
        ),
      );

      LoggerService.info('Profile update response: ${response.statusCode}');
      LoggerService.info('Response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic>) {
          // API now returns profile data directly, just return it as-is
          // The calling code will check for 'id' field to determine success
          return responseData;
        } else {
          return {'status': 200, 'message': 'Profile updated successfully'};
        }
      } else {
        LoggerService.error(
            'Server error: ${response.statusCode} - ${response.data}');
        return {
          'status': response.statusCode,
          'error': 'Server error: ${response.statusCode} - ${response.data}'
        };
      }
    } catch (e) {
      LoggerService.error('Profile update error: $e', e);
      return {
        'status': 500,
        'error': 'Failed to update profile: ${e.toString()}'
      };
    }
  }
}
