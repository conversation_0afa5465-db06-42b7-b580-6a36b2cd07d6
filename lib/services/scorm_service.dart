import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/scorm_progress_model.dart';
import '../repositories/scorm_progress_repository.dart';
import '../services/api_service.dart';
import '../services/logger_service.dart';

/// Service for managing SCORM progress and synchronization
class ScormService {
  static final ScormService _instance = ScormService._internal();
  factory ScormService() => _instance;
  ScormService._internal();

  final ScormProgressRepository _repository = ScormProgressRepository();
  final Connectivity _connectivity = Connectivity();

  /// Save SCORM progress locally
  Future<bool> saveProgress({
    required int userId,
    required int moduleId,
    required Map<String, dynamic> cmiData,
  }) async {
    try {
      // Log the incoming suspend_data
      final suspendData = cmiData['cmi.suspend_data'];
      if (suspendData != null) {
        LoggerService.debug('SCORM: Saving suspend_data length: ${suspendData.toString().length}');
        LoggerService.debug('SCORM: Saving suspend_data preview: ${suspendData.toString().substring(0, suspendData.toString().length > 50 ? 50 : suspendData.toString().length)}...');

        // Check for the corrupted "}" value
        if (suspendData.toString() == "}") {
          LoggerService.warning('SCORM: Detected corrupted suspend_data: "}"');
        }
      }

      // Get existing progress if any
      final existingProgress = await _repository.getProgress(userId, moduleId);

      ScormProgress progress;
      if (existingProgress != null) {
        // Update existing progress
        progress = existingProgress.copyWith(
          scormData: cmiData,
          lastAccessed: DateTime.now(),
          syncStatus: false, // Mark as needing sync
          updatedAt: DateTime.now(),
        );
      } else {
        // Create new progress
        progress = ScormProgress.fromCmiData(
          userId: userId,
          moduleId: moduleId,
          cmiData: cmiData,
        );
      }

      final result = await _repository.saveProgress(progress);

      if (result > 0) {
        LoggerService.debug('SCORM progress saved locally for user $userId, module $moduleId');

        // Try to sync if online
        await _trySyncProgress(userId, moduleId);

        return true;
      }

      return false;
    } catch (e) {
      LoggerService.error('Error saving SCORM progress', e);
      return false;
    }
  }

  /// Get SCORM progress for a module
  Future<ScormProgress?> getProgress(int userId, int moduleId) async {
    try {
      return await _repository.getProgress(userId, moduleId);
    } catch (e) {
      LoggerService.error('Error getting SCORM progress', e);
      return null;
    }
  }

  /// Get all progress for a user
  Future<List<ScormProgress>> getUserProgress(int userId) async {
    try {
      return await _repository.getUserProgress(userId);
    } catch (e) {
      LoggerService.error('Error getting user SCORM progress', e);
      return [];
    }
  }

  /// Get SCORM data as JSON string for HTML initialization
  Future<String> getScormDataForModule(int userId, int moduleId) async {
    try {
      final progress = await _repository.getProgress(userId, moduleId);

      if (progress != null) {
        // Log the suspend_data before encoding
        final suspendData = progress.scormData['cmi.suspend_data'];
        if (suspendData != null) {
          LoggerService.debug('SCORM: Retrieved suspend_data length: ${suspendData.toString().length}');
          LoggerService.debug('SCORM: Retrieved suspend_data preview: ${suspendData.toString().substring(0, suspendData.toString().length > 50 ? 50 : suspendData.toString().length)}...');
        }

        // Update last accessed time
        await _repository.updateLastAccessed(userId, moduleId);

        // Return SCORM data as JSON string
        final jsonString = jsonEncode(progress.scormData);
        LoggerService.debug('SCORM: Encoded JSON length: ${jsonString.length}');

        return jsonString;
      }

      // Return empty object if no progress found
      LoggerService.debug('SCORM: No progress found for user $userId, module $moduleId');
      return '{}';
    } catch (e) {
      LoggerService.error('Error getting SCORM data for module', e);
      return '{}';
    }
  }

  /// Check if device is online
  Future<bool> _isOnline() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      LoggerService.error('Error checking connectivity', e);
      return false;
    }
  }

  /// Try to sync specific progress if online
  Future<void> _trySyncProgress(int userId, int moduleId) async {
    try {
      if (await _isOnline()) {
        final progress = await _repository.getProgress(userId, moduleId);
        if (progress != null && progress.needsSync) {
          await _syncSingleProgress(progress);
        }
      }
    } catch (e) {
      LoggerService.error('Error trying to sync progress', e);
    }
  }

  /// Sync all unsynced progress to server
  Future<int> syncAllProgress() async {
    try {
      if (!await _isOnline()) {
        LoggerService.debug('Device is offline, skipping SCORM sync');
        return 0;
      }

      final unsyncedProgress = await _repository.getUnsyncedProgress();
      
      if (unsyncedProgress.isEmpty) {
        LoggerService.debug('No unsynced SCORM progress found');
        return 0;
      }

      int syncedCount = 0;
      
      for (final progress in unsyncedProgress) {
        try {
          final success = await _syncSingleProgress(progress);
          if (success) {
            syncedCount++;
          }
        } catch (e) {
          LoggerService.error('Error syncing progress for module ${progress.moduleId}', e);
        }
      }

      LoggerService.debug('Synced $syncedCount out of ${unsyncedProgress.length} SCORM progress records');
      return syncedCount;
    } catch (e) {
      LoggerService.error('Error syncing all SCORM progress', e);
      return 0;
    }
  }

  /// Sync single progress record to server
  Future<bool> _syncSingleProgress(ScormProgress progress) async {
    try {
      // Prepare data for API using the new format
      final syncData = {
        'module_id': progress.moduleId.toString(),
        'state': progress.scormData,
        'completion_status': progress.isCompleted ? 1 : 0,
      };

      // Use the new unified sync endpoint
      final response = await ApiService.syncModuleProgress(syncData);

      if (response['status'] == 200) {
        // Mark as synced
        await _repository.updateSyncStatus(progress.id!, true);
        LoggerService.debug('Successfully synced SCORM progress for module ${progress.moduleId}');
        return true;
      } else {
        // Increment sync attempts
        final updatedProgress = progress.incrementSyncAttempts();
        await _repository.saveProgress(updatedProgress);
        LoggerService.warning('Failed to sync SCORM progress: ${response['message']}');
        return false;
      }
    } catch (e) {
      // Increment sync attempts on error
      try {
        final updatedProgress = progress.incrementSyncAttempts();
        await _repository.saveProgress(updatedProgress);
      } catch (updateError) {
        LoggerService.error('Error updating sync attempts', updateError);
      }
      
      LoggerService.error('Error syncing SCORM progress', e);
      return false;
    }
  }

  /// Get sync statistics
  Future<Map<String, int>> getSyncStats(int userId) async {
    try {
      final allProgress = await _repository.getUserProgress(userId);
      final unsyncedProgress = await _repository.getUnsyncedProgress();
      final userUnsyncedCount = unsyncedProgress.where((p) => p.userId == userId).length;
      
      return {
        'total': allProgress.length,
        'synced': allProgress.length - userUnsyncedCount,
        'unsynced': userUnsyncedCount,
        'completed': allProgress.where((p) => p.isCompleted).length,
      };
    } catch (e) {
      LoggerService.error('Error getting sync stats', e);
      return {
        'total': 0,
        'synced': 0,
        'unsynced': 0,
        'completed': 0,
      };
    }
  }

  /// Clear all progress for a user (useful for logout)
  Future<void> clearUserProgress(int userId) async {
    try {
      await _repository.deleteUserProgress(userId);
      LoggerService.debug('Cleared all SCORM progress for user $userId');
    } catch (e) {
      LoggerService.error('Error clearing user SCORM progress', e);
    }
  }

  /// Force sync specific module progress
  Future<bool> forceSyncModule(int userId, int moduleId) async {
    try {
      final progress = await _repository.getProgress(userId, moduleId);
      if (progress != null) {
        return await _syncSingleProgress(progress);
      }
      return false;
    } catch (e) {
      LoggerService.error('Error force syncing module progress', e);
      return false;
    }
  }
}
