import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../services/logger_service.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage();

  // Keys
  static const String _tokenKey = 'auth_token';

  // Save token
  static Future<void> saveToken(String token) async {
    try {
      await _storage.write(key: _tokenKey, value: token);
      LoggerService.debug('Token saved successfully');
    } catch (e) {
      LoggerService.error('Error saving token', e);
      throw Exception('Failed to save authentication token: ${e.toString()}');
    }
  }

  // Get token
  static Future<String?> getToken() async {
    try {
      final token = await _storage.read(key: _tokenKey);
      if (token == null) {
        LoggerService.debug('No token found in secure storage');
      } else {
        LoggerService.debug('Token retrieved successfully from secure storage');
      }
      return token;
    } catch (e) {
      LoggerService.error('Error retrieving token from secure storage', e);
      throw Exception('Failed to retrieve authentication token: ${e.toString()}');
    }
  }

  // Delete token (for logout)
  static Future<void> deleteToken() async {
    try {
      await _storage.delete(key: _tokenKey);
      LoggerService.debug('Token deleted successfully');
    } catch (e) {
      LoggerService.error('Error deleting token', e);
      throw Exception('Failed to delete authentication token: ${e.toString()}');
    }
  }

  // Check if token exists
  static Future<bool> hasToken() async {
    try {
      final token = await getToken();
      final hasValidToken = token != null && token.isNotEmpty;
      LoggerService.debug('Token exists check: $hasValidToken');
      return hasValidToken;
    } catch (e) {
      LoggerService.error('Error checking if token exists', e);
      return false;
    }
  }
}
