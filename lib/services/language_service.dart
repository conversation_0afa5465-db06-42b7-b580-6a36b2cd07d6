import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import 'logger_service.dart';

/// Service to manage application language settings
class LanguageService {
  static const String _languageKey = 'app_language';

  /// Get the current locale from shared preferences
  static Future<Locale> getCurrentLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? languageCode = prefs.getString(_languageKey);

      // If language is set and enabled, use it
      if (languageCode != null && AppConfig.isLocaleEnabled(languageCode)) {
        LoggerService.debug('Using saved language: $languageCode');
        return Locale(languageCode);
      }

      // If device locale should be used and is supported
      if (AppConfig.useDeviceLocale) {
        final deviceLocale = PlatformDispatcher.instance.locale;
        if (AppConfig.isLocaleEnabled(deviceLocale.languageCode)) {
          LoggerService.debug('Using device language: ${deviceLocale.languageCode}');
          return Locale(deviceLocale.languageCode);
        }
      }

      // Fall back to default locale
      LoggerService.debug('Using default language: ${AppConfig.defaultLocale}');
      return Locale(AppConfig.defaultLocale);
    } catch (e) {
      LoggerService.error('Error getting current locale', e);
      return Locale(AppConfig.defaultLocale);
    }
  }

  /// Set the application locale
  static Future<bool> setLocale(String languageCode) async {
    try {
      if (!AppConfig.isLocaleEnabled(languageCode)) {
        LoggerService.warning('Attempted to set unsupported language: $languageCode');
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);
      LoggerService.info('Language set to: $languageCode');
      return true;
    } catch (e) {
      LoggerService.error('Error setting language', e);
      return false;
    }
  }

  /// Get the list of available languages for the UI
  static List<Map<String, String>> getAvailableLanguages() {
    return AppConfig.getEnabledLocalesWithNames();
  }

  /// Get the current language code
  static Future<String> getCurrentLanguageCode() async {
    final locale = await getCurrentLocale();
    return locale.languageCode;
  }
}
