import 'package:shared_preferences/shared_preferences.dart';
import '../services/logger_service.dart';

/// Service to manage first-time app launch detection
class FirstLaunchService {
  static const String _firstLaunchKey = 'is_first_launch';

  /// Check if this is the first time the app is being launched
  static Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFirstLaunch = prefs.getBool(_firstLaunchKey) ?? true;
      LoggerService.debug('First launch check: $isFirstLaunch');
      return isFirstLaunch;
    } catch (e) {
      LoggerService.error('Error checking first launch status', e);
      // If there's an error, assume it's first launch to be safe
      return true;
    }
  }

  /// Mark the app as having been launched (no longer first launch)
  static Future<bool> markAsLaunched() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstLaunchKey, false);
      LoggerService.info('App marked as launched (no longer first launch)');
      return true;
    } catch (e) {
      LoggerService.error('Error marking app as launched', e);
      return false;
    }
  }

  /// Reset first launch status (useful for testing or app reset)
  static Future<bool> resetFirstLaunchStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstLaunchKey, true);
      LoggerService.info('First launch status reset');
      return true;
    } catch (e) {
      LoggerService.error('Error resetting first launch status', e);
      return false;
    }
  }
}
