import '../models/course_model.dart';
import '../services/course_service.dart';
import '../services/logger_service.dart';

/// Service for fetching course recommendations based on career guidance results
class CourseRecommendationService {
  /// Get courses by category/tag for career recommendations
  static Future<List<Course>> getCoursesByCategory(String category) async {
    try {
      LoggerService.info('Fetching courses for category: $category');

      // Use existing CourseService to get all courses
      final allCourses = await CourseService.fetchCourses();

      // Filter courses by category/tag
      final filteredCourses = allCourses.where((course) {
        // Check if course tags contain the category
        if (course.tags.isNotEmpty) {
          final hasMatchingTag = course.tags.any((tag) => 
            tag.toLowerCase().contains(category.toLowerCase()) ||
            category.toLowerCase().contains(tag.toLowerCase())
          );
          if (hasMatchingTag) return true;
        }
        
        // Check if course category matches
        if (course.category.isNotEmpty) {
          final hasMatchingCategory = course.category.toLowerCase().contains(category.toLowerCase()) ||
              category.toLowerCase().contains(course.category.toLowerCase());
          if (hasMatchingCategory) return true;
        }
        
        // Check if course title or description contains category keywords
        final categoryKeywords = _getCategoryKeywords(category);
        final courseText = '${course.title} ${course.description}'.toLowerCase();
        
        return categoryKeywords.any((keyword) => 
          courseText.contains(keyword.toLowerCase())
        );
      }).toList();

      LoggerService.info('Found ${filteredCourses.length} courses for category: $category');
      return filteredCourses;
    } catch (e) {
      LoggerService.error('Error fetching courses by category', e);
      return [];
    }
  }

  /// Get category-specific keywords for better matching
  static List<String> _getCategoryKeywords(String category) {
    final categoryLower = category.toLowerCase();
    
    switch (categoryLower) {
      case 'realistic':
        return [
          'realistic', 'practical', 'hands-on', 'technical', 'mechanical',
          'engineering', 'construction', 'agriculture', 'automotive', 'repair',
          'building', 'manufacturing', 'tools', 'machinery', 'craft'
        ];
      case 'investigative':
        return [
          'investigative', 'research', 'science', 'analysis', 'laboratory',
          'data', 'study', 'experiment', 'theory', 'mathematics', 'physics',
          'chemistry', 'biology', 'computer science', 'technology'
        ];
      case 'artistic':
        return [
          'artistic', 'creative', 'design', 'art', 'music', 'writing',
          'literature', 'painting', 'drawing', 'photography', 'theater',
          'dance', 'film', 'media', 'graphics', 'fashion'
        ];
      case 'social':
        return [
          'social', 'people', 'helping', 'teaching', 'counseling', 'healthcare',
          'education', 'community', 'service', 'psychology', 'social work',
          'nursing', 'therapy', 'human resources', 'communication'
        ];
      case 'enterprising':
        return [
          'enterprising', 'business', 'leadership', 'management', 'sales',
          'marketing', 'entrepreneurship', 'finance', 'economics', 'politics',
          'law', 'administration', 'negotiation', 'persuasion', 'commerce'
        ];
      case 'conventional':
        return [
          'conventional', 'organization', 'administration', 'office', 'clerical',
          'accounting', 'bookkeeping', 'data entry', 'filing', 'records',
          'procedures', 'systematic', 'detail-oriented', 'structured'
        ];
      default:
        return [categoryLower];
    }
  }

  /// Get recommended courses based on RIASEC results
  static Future<List<Course>> getRecommendedCourses(Map<String, double> riasecScores) async {
    try {
      LoggerService.info('Getting course recommendations based on RIASEC scores');

      // Get the top 2 categories
      final sortedCategories = riasecScores.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));
      
      final topCategories = sortedCategories.take(2).map((e) => e.key).toList();
      
      LoggerService.info('Top categories for recommendations: $topCategories');

      // Get courses for each top category
      final List<Course> recommendedCourses = [];
      
      for (final category in topCategories) {
        final categoryCourses = await getCoursesByCategory(category);
        recommendedCourses.addAll(categoryCourses);
      }

      // Remove duplicates and limit to 10 courses
      final uniqueCourses = <Course>[];
      final seenIds = <int>{};
      
      for (final course in recommendedCourses) {
        if (!seenIds.contains(course.id)) {
          seenIds.add(course.id);
          uniqueCourses.add(course);
          if (uniqueCourses.length >= 10) break;
        }
      }

      LoggerService.info('Returning ${uniqueCourses.length} recommended courses');
      return uniqueCourses;
    } catch (e) {
      LoggerService.error('Error getting recommended courses', e);
      return [];
    }
  }
}
