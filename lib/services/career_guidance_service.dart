import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../models/career_guidance_model.dart';
import '../models/question_model.dart';
import '../services/secure_storage_service.dart';
import '../services/logger_service.dart';

class CareerGuidanceService {
  /// Get authentication headers
  static Future<Map<String, String>> _getAuthHeaders() async {
    try {
      final token = await SecureStorageService.getToken();
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        if (token != null && token.isNotEmpty) 'Authorization': 'Bearer $token',
      };

      LoggerService.debug('Auth headers prepared: ${headers.keys.join(', ')}');
      if (token != null && token.isNotEmpty) {
        LoggerService.debug(
            'Bearer token present: ${token.substring(0, 10)}...');
      } else {
        LoggerService.warning('No bearer token available');
      }

      return headers;
    } catch (e) {
      LoggerService.error('Error getting auth headers', e);
      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
    }
  }

  /// Fetch career guidance questions from API
  static Future<List<CareerQuestionCategory>> getQuestions() async {
    try {
      LoggerService.info('Fetching career guidance questions...');

      final headers = await _getAuthHeaders();
      final url = Uri.parse(
          '${AppConfig.apiBaseUrl}${AppConfig.apiCareerGuidanceQuestions}');

      LoggerService.debug('GET Request URL: $url');
      LoggerService.debug('GET Request Headers: $headers');

      final response = await http.get(url, headers: headers);

      LoggerService.debug(
          'Questions API response status: ${response.statusCode}');
      LoggerService.debug('Questions API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = utf8.decode(response.bodyBytes);
        final List<dynamic> jsonData = jsonDecode(data);
        final categories = jsonData
            .map(
                (categoryJson) => CareerQuestionCategory.fromJson(categoryJson))
            .toList();

        LoggerService.info(
            'Successfully fetched ${categories.length} question categories');
        return categories;
      } else {
        LoggerService.error(
            'Failed to fetch questions. Status: ${response.statusCode}');
        LoggerService.error('Response body: ${response.body}');
        throw Exception('Failed to fetch questions: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error fetching career guidance questions', e);
      rethrow;
    }
  }

  /// Submit quiz responses to API and get results
  static Future<CareerGuidanceResult> submitResponses(
      List<CareerQuestion> allAnsweredQuestions) async {
    try {
      LoggerService.info('Submitting career guidance responses...');

      final headers = await _getAuthHeaders();
      final url = Uri.parse(
          '${AppConfig.apiBaseUrl}${AppConfig.apiCareerGuidanceResponse}');

      // Convert questions to response format
      final responses = allAnsweredQuestions
          .where((q) => q.answer != null && q.id != null)
          .map((q) => CareerQuestionResponse(
                id: q.id!,
                answer: q.answer!.value,
              ))
          .toList();
      // Send direct array format as required by server
      final requestBody = responses.map((r) => r.toJson()).toList();
      final requestBodyJson = json.encode(requestBody);

      LoggerService.info(
          'Submitting ${responses.length} responses in direct array format');
      LoggerService.debug('POST Request URL: $url');
      LoggerService.debug('POST Request Headers: $headers');
      LoggerService.debug('POST Request Body: $requestBodyJson');

      final response = await http.post(
        url,
        headers: headers,
        body: requestBodyJson,
      );

      LoggerService.debug('Submit response API status: ${response.statusCode}');
      LoggerService.debug('Submit response API body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = utf8.decode(response.bodyBytes);
        final Map<String, dynamic> jsonData = json.decode(data);
        final result = CareerGuidanceResult.fromJson(jsonData);

        LoggerService.info(
            'Successfully submitted responses and received results');
        return result;
      } else {
        LoggerService.error(
            'Failed to submit responses. Status: ${response.statusCode}');
        LoggerService.error('Response body: ${response.body}');
        throw Exception('Failed to submit responses: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error submitting career guidance responses', e);
      rethrow;
    }
  }

  /// Get existing results for user from API
  static Future<CareerGuidanceResult?> getExistingResults() async {
    try {
      LoggerService.info('Fetching existing career guidance results...');

      final headers = await _getAuthHeaders();
      final url = Uri.parse(
          '${AppConfig.apiBaseUrl}${AppConfig.apiCareerGuidanceResponse}');

      LoggerService.debug('GET Results Request URL: $url');
      LoggerService.debug('GET Results Request Headers: $headers');

      final response = await http.get(url, headers: headers);

      LoggerService.debug(
          'Get results API response status: ${response.statusCode}');
      LoggerService.debug('Get results API response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = utf8.decode(response.bodyBytes);
        final Map<String, dynamic> jsonData = json.decode(data);

        // Check if user has results
        if (jsonData.containsKey('scores') && jsonData['scores'] != null) {
          final result = CareerGuidanceResult.fromJson(jsonData);
          LoggerService.info('Successfully fetched existing results');
          return result;
        } else {
          LoggerService.info('No existing results found for user');
          return null;
        }
      } else if (response.statusCode == 404) {
        LoggerService.info('No existing results found for user (404)');
        return null;
      } else {
        LoggerService.error(
            'Failed to fetch existing results. Status: ${response.statusCode}');
        LoggerService.error('Response body: ${response.body}');
        throw Exception(
            'Failed to fetch existing results: ${response.statusCode}');
      }
    } catch (e) {
      LoggerService.error('Error fetching existing career guidance results', e);
      rethrow;
    }
  }

  /// Calculate local RIASEC scores (fallback if API fails)
  static CareerGuidanceResult calculateLocalScores(
      List<CareerQuestion> allAnsweredQuestions) {
    try {
      LoggerService.info('Calculating local RIASEC scores...');

      // Group questions by category
      Map<String, List<CareerQuestion>> categoryQuestions = {};
      for (final question in allAnsweredQuestions) {
        // Extract category from the question context (you'll need to track this)
        // For now, we'll use a simple approach
        final category = _extractCategoryFromQuestion(question);
        if (category != null) {
          categoryQuestions[category] ??= [];
          categoryQuestions[category]!.add(question);
        }
      }

      // Calculate scores for each category
      List<RiasecScore> scores = [];
      for (final category in RiasecCategory.values) {
        final categoryName = category.value;
        final questions = categoryQuestions[categoryName] ?? [];

        if (questions.isNotEmpty) {
          final totalScore = questions
              .where((q) => q.answer != null)
              .map((q) => q.answer!.scoreValue)
              .fold(0, (sum, score) => sum + score);

          final maxPossibleScore =
              questions.length * 5; // Max score per question is 5
          final percentage = (totalScore / maxPossibleScore) * 100;

          scores.add(RiasecScore(name: categoryName, score: percentage));
        } else {
          scores.add(RiasecScore(name: categoryName, score: 0.0));
        }
      }

      // Find highest scoring category
      scores.sort((a, b) => b.score.compareTo(a.score));
      final highest = scores.first;

      final highestCategory = HighestCategory(
        name: highest.name,
        description: _getDefaultDescription(highest.name),
      );

      LoggerService.info(
          'Local calculation completed. Highest: ${highest.name} (${highest.score.toStringAsFixed(1)}%)');

      return CareerGuidanceResult(
        scores: scores,
        highestCategory: highestCategory,
        completedAt: DateTime.now(),
      );
    } catch (e) {
      LoggerService.error('Error calculating local RIASEC scores', e);
      rethrow;
    }
  }

  /// Extract category from question (helper method)
  static String? _extractCategoryFromQuestion(CareerQuestion question) {
    // This is a simplified approach - in a real implementation,
    // you'd track the category when the question is presented
    // For now, return null and handle in the provider
    return null;
  }

  /// Get default description for category
  static String _getDefaultDescription(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'realistic':
        return 'You prefer hands-on work and practical problem-solving. Consider careers in engineering, construction, agriculture, or technical fields.';
      case 'investigative':
        return 'You enjoy research, analysis, and intellectual challenges. Consider careers in science, research, medicine, or technology.';
      case 'artistic':
        return 'You value creativity and self-expression. Consider careers in arts, design, writing, music, or entertainment.';
      case 'social':
        return 'You enjoy helping others and working with people. Consider careers in education, healthcare, counseling, or social services.';
      case 'enterprising':
        return 'You enjoy leadership and business challenges. Consider careers in business, sales, management, or entrepreneurship.';
      case 'conventional':
        return 'You prefer organized, structured work environments. Consider careers in administration, finance, accounting, or data management.';
      default:
        return 'Explore various career options that match your interests and strengths.';
    }
  }
}
