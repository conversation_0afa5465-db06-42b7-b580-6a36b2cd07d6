import 'dart:io';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../config/app_config.dart';
import '../models/library_item_model.dart';
import '../models/course_model.dart';
import '../screens/courses/webview_screen.dart';
import '../screens/courses/download_screen.dart';
import '../screens/library/pdf_viewer_screen.dart';
import '../screens/media/media_player_screen.dart';
import '../utils/scorm_player.dart';
import '../services/logger_service.dart';
import '../localization/app_localizations_extension.dart';

class ContentHandlerService {
  static Future<bool> openLibraryItem(
    BuildContext? context,
    LibraryItem item, {
    Function(String)? onError,
    bool showErrorSnackbar = true,
  }) async {
    try {
      LoggerService.info('Opening library item: ${item.contentTitle}');

      // Handle external links (requires context)
      if (item.isExternalLink) {
        if (context != null) {
          _openExternalLink(context, item);
          return true;
        } else {
          final errorMessage = context?.tr('errors.no_context_external_link') ??
              'Cannot open external link without UI context';
          LoggerService.warning(errorMessage);
          if (onError != null) {
            onError(errorMessage);
          }
          return false;
        }
      }

      // Handle PDF files with in-app PDF viewer (regardless of library_downloadable setting)
      if (item.contentType.toLowerCase() == 'pdf document' &&
          item.contentFile.isNotEmpty &&
          context != null) {
        final String pdfUrl = '${AppConfig.apiBaseUrl}${item.contentFile}';
        try {
          LoggerService.info('Opening PDF with in-app viewer: $pdfUrl');

          if (context.mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PDFViewerScreen(
                  pdfUrl: pdfUrl,
                  title: item.contentTitle,
                ),
              ),
            );
            return true;
          }
        } catch (e) {
          LoggerService.error(
              'Error opening PDF with in-app viewer: $pdfUrl', e);
        }
        // If in-app PDF viewer fails, fall through to WebView
      }

      // Check if library_downloadable is false - if so, open all content directly in WebView
      if (!AppConfig.libraryDownloadable &&
          item.contentFile.isNotEmpty &&
          context != null) {
        LoggerService.info(
            'library_downloadable is false, opening content file directly in WebView');
        _openContentFileInWebView(context, item);
        return true;
      }

      // Handle downloaded files
      if (item.isDownloaded && item.localFilePath != null) {
        return await _openLocalFile(
          item.localFilePath!,
          onError: onError,
          showErrorSnackbar: showErrorSnackbar && context != null,
          context: showErrorSnackbar && context != null ? context : null,
        );
      }

      // If the item is not downloaded and has a content link (requires context)
      if (item.contentLink.isNotEmpty) {
        if (context != null) {
          _openExternalLink(context, item);
          return true;
        } else {
          final errorMessage = context?.tr('errors.no_context_content_link') ??
              'Cannot open content link without UI context';
          LoggerService.warning(errorMessage);
          if (onError != null) {
            onError(errorMessage);
          }
          return false;
        }
      }

      // If library_downloadable is false and we have a content file but no context, return an error
      if (!AppConfig.libraryDownloadable && item.contentFile.isNotEmpty) {
        final errorMessage = context?.tr('errors.no_context_content_file') ??
            'Cannot open content file without UI context';
        LoggerService.warning(errorMessage);
        if (onError != null) {
          onError(errorMessage);
        }
        return false;
      }

      // If we reach here, the item is neither downloaded nor has a content link
      final errorMessage = context
              ?.tr('errors.content_not_available_offline') ??
          'Content not available for offline viewing. Please download first.';
      if (onError != null) {
        onError(errorMessage);
      }

      if (showErrorSnackbar && context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    } catch (e) {
      final errorMessage = context?.tr('errors.failed_to_open_item',
              args: {'error': e.toString()}) ??
          'Failed to open item: ${e.toString()}';
      LoggerService.error(errorMessage, e);

      if (onError != null) {
        onError(errorMessage);
      }

      if (showErrorSnackbar && context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    }
  }

  /// Opens a local file using the appropriate system app
  static Future<bool> _openLocalFile(
    String filePath, {
    Function(String)? onError,
    bool showErrorSnackbar = false,
    BuildContext? context,
  }) async {
    try {
      final file = File(filePath);

      // Check if file exists
      if (await file.exists()) {
        // Check if file is readable and not empty
        final fileSize = await file.length();
        if (fileSize <= 0) {
          final errorMessage = context?.tr('errors.file_empty_corrupted') ??
              'File is empty or corrupted';
          LoggerService.warning(errorMessage);

          if (onError != null) {
            onError(errorMessage);
          }

          if (showErrorSnackbar && context != null && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: Colors.red,
              ),
            );
          }

          return false;
        }

        // Open the file with the appropriate app
        final result = await OpenFilex.open(filePath);

        if (result.type != ResultType.done) {
          final errorMessage = context?.tr('errors.failed_to_open_file',
                  args: {'error': result.message}) ??
              'Failed to open file: ${result.message}';
          LoggerService.warning(errorMessage);

          if (onError != null) {
            onError(errorMessage);
          }

          if (showErrorSnackbar && context != null && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: Colors.red,
              ),
            );
          }

          return false;
        }

        return true;
      } else {
        final errorMessage = context?.tr('errors.file_not_found') ??
            'File not found. It may have been deleted.';
        LoggerService.warning('File does not exist: $filePath');

        // Update the database to reflect that the file is no longer available
        // This would need to be handled by the caller since we don't have the item ID here

        if (onError != null) {
          onError(errorMessage);
        }

        if (showErrorSnackbar && context != null && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }

        return false;
      }
    } catch (e) {
      final errorMessage = context?.tr('errors.error_accessing_file',
              args: {'error': e.toString()}) ??
          'Error accessing file: ${e.toString()}';
      LoggerService.error(errorMessage, e);

      if (onError != null) {
        onError(errorMessage);
      }

      if (showErrorSnackbar && context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    }
  }

  /// Opens an external link in a WebView
  /// Requires a non-null context
  static void _openExternalLink(BuildContext context, LibraryItem item) {
    if (context.mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewScreen(url: item.contentLink),
        ),
      );
    } else {
      LoggerService.warning(
          'Cannot open external link: context is not mounted');
    }
  }

  /// Opens a content file directly in WebView without downloading
  /// Requires a non-null context
  static void _openContentFileInWebView(
      BuildContext context, LibraryItem item) {
    if (context.mounted) {
      const String baseUrl = AppConfig.apiBaseUrl;
      final String contentFileUrl = '$baseUrl${item.contentFile}';

      LoggerService.info('Opening content file in WebView: $contentFileUrl');

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewScreen(url: contentFileUrl),
        ),
      );
    } else {
      LoggerService.warning(
          'Cannot open content file in WebView: context is not mounted');
    }
  }

  /// Opens a SCORM module in a WebView
  static Future<void> openScormModule(BuildContext context, String url) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewScreen(url: url),
      ),
    );
  }

  /// Opens an enrolled module with progress tracking
  static Future<bool> openEnrolledModule(
    BuildContext context,
    EnrolledModule module, {
    String? courseSlug,
    Function(String)? onError,
    bool showErrorSnackbar = true,
  }) async {
    try {
      LoggerService.info('Opening enrolled module: ${module.id}');

      if (module.content == null) {
        final errorMessage = 'Module content is not available';
        LoggerService.warning(errorMessage);
        if (onError != null) {
          onError(errorMessage);
        }
        return false;
      }

      final content = module.content!;

      // Handle different content types
      if (content.isPdf) {
        // Open PDF with module ID for progress tracking
        final String pdfUrl = '${AppConfig.apiBaseUrl}${content.downloadLink}';

        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PDFViewerScreen(
                pdfUrl: pdfUrl,
                title: 'Module ${module.id}',
                moduleId: module.id,
              ),
            ),
          );
          return true;
        }
      } else if (content.isAudioVideo) {
        // Open audio/video with module ID for progress tracking
        final String mediaUrl = '${AppConfig.apiBaseUrl}${content.downloadLink}';

        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => MediaPlayerScreen(
                mediaUrl: mediaUrl,
                title: 'Module ${module.id}',
                moduleId: module.id,
              ),
            ),
          );
          return true;
        }
      } else if (content.isScorm) {
        // Handle SCORM content with proper offline support
        return await _handleScormModule(context, module, courseSlug);
      } else {
        // Fallback to WebView for unknown content types
        final String contentUrl = '${AppConfig.apiBaseUrl}${content.downloadLink}';

        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => WebViewScreen(url: contentUrl),
            ),
          );
          return true;
        }
      }

      return false;
    } catch (e) {
      LoggerService.error('Error opening enrolled module', e);
      final errorMessage = 'Failed to open module: ${e.toString()}';

      if (onError != null) {
        onError(errorMessage);
      }

      if (showErrorSnackbar && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }

      return false;
    }
  }

  /// Handle SCORM module with offline support and download functionality
  static Future<bool> _handleScormModule(BuildContext context, EnrolledModule module, String? courseSlug) async {
    try {
      // Check if device is online
      final connectivityResult = await Connectivity().checkConnectivity();
      final isOnline = connectivityResult != ConnectivityResult.none;

      // Check if module is downloaded locally
      final isDownloaded = await _isModuleDownloaded(module.id);

      if (isDownloaded) {
        // Downloaded -> Always play offline version (regardless of online status)
        return await _playScormOffline(context, module);
      } else if (isOnline) {
        // Not Downloaded & Online -> Show options (play online or download)
        return await _showScormOptions(context, module, courseSlug);
      } else {
        // Not Downloaded & Offline -> Show error
        _showOfflineError(context);
        return false;
      }
    } catch (e) {
      LoggerService.error('Error handling SCORM module', e);
      return false;
    }
  }

  /// Check if a module is downloaded locally
  static Future<bool> _isModuleDownloaded(int moduleId) async {
    try {
      final externalDirectories = await getExternalStorageDirectories();
      if (externalDirectories == null || externalDirectories.isEmpty) {
        return false;
      }

      for (var externalDir in externalDirectories) {
        // Check for the pattern: my_courses/*/module_$moduleId
        final myCoursesDir = Directory('${externalDir.path}/my_courses');
        if (await myCoursesDir.exists()) {
          await for (var courseDir in myCoursesDir.list()) {
            if (courseDir is Directory) {
              final modulePath = '${courseDir.path}/module_$moduleId';
              if (await Directory(modulePath).exists()) {
                return true;
              }
            }
          }
        }
      }
      return false;
    } catch (e) {
      LoggerService.error('Error checking if module is downloaded', e);
      return false;
    }
  }

  /// Play SCORM module offline
  static Future<bool> _playScormOffline(BuildContext context, EnrolledModule module) async {
    try {
      final externalDirectories = await getExternalStorageDirectories();
      if (externalDirectories == null || externalDirectories.isEmpty) {
        return false;
      }

      String? modulePath;
      String? externalDirPath;
      for (var externalDir in externalDirectories) {
        // Search for the module in any course directory
        final myCoursesDir = Directory('${externalDir.path}/my_courses');
        if (await myCoursesDir.exists()) {
          await for (var courseDir in myCoursesDir.list()) {
            if (courseDir is Directory) {
              final path = '${courseDir.path}/module_${module.id}';
              if (await Directory(path).exists()) {
                modulePath = path;
                externalDirPath = externalDir.path;
                break;
              }
            }
          }
          if (modulePath != null) break;
        }
      }

      if (modulePath != null && externalDirPath != null && module.content?.scormDataPath != null) {
        // Convert absolute path to relative path from external storage directory
        final relativePath = modulePath.substring(externalDirPath.length + 1); // +1 to remove leading slash
        final scormDataPath = '$relativePath/${module.content!.scormDataPath}';

        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ScormPlayer(
                scormDataPath: scormDataPath,
                moduleName: 'Module ${module.id}',
                moduleId: module.id,
                courseId: module.id, // Using module ID as course ID for enrolled modules
              ),
            ),
          );
          return true;
        }
      }
      return false;
    } catch (e) {
      LoggerService.error('Error playing SCORM offline', e);
      return false;
    }
  }

  /// Show SCORM options (play online or download)
  static Future<bool> _showScormOptions(BuildContext context, EnrolledModule module, String? courseSlug) async {
    try {
      final result = await showModalBottomSheet<bool>(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
        ),
        builder: (context) => SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.play_circle),
                title: Text(context.tr('courses.watch_online')),
                onTap: () {
                  Navigator.pop(context, true);
                },
              ),
              ListTile(
                leading: const Icon(Icons.download),
                title: Text(context.tr('courses.download')),
                onTap: () {
                  Navigator.pop(context, false);
                },
              ),
            ],
          ),
        ),
      );

      if (result == true) {
        // Play online
        return await _playScormOnline(context, module);
      } else if (result == false) {
        // Download module
        return await _downloadScormModule(context, module, courseSlug);
      }

      return false;
    } catch (e) {
      LoggerService.error('Error showing SCORM options', e);
      return false;
    }
  }

  /// Play SCORM module online
  static Future<bool> _playScormOnline(BuildContext context, EnrolledModule module) async {
    try {
      // For enrolled modules, we need to construct the proper SCORM URL
      // This might need to be adjusted based on your API structure
      final String scormUrl = '${AppConfig.apiBaseUrl}${module.content!.downloadLink}';

      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => WebViewScreen(url: scormUrl),
          ),
        );
        return true;
      }
      return false;
    } catch (e) {
      LoggerService.error('Error playing SCORM online', e);
      return false;
    }
  }

  /// Download SCORM module
  static Future<bool> _downloadScormModule(BuildContext context, EnrolledModule module, String? courseSlug) async {
    try {
      if (context.mounted) {
        final result = await Navigator.push<bool>(
          context,
          MaterialPageRoute(
            builder: (context) => DownloadPage(
              courseSlug: courseSlug ?? 'enrolled_course_${module.id}', // Use provided slug or generate one
              moduleSlug: 'module_${module.id}',
              downloadLink: module.content!.downloadLink,
            ),
          ),
        );
        return result ?? false;
      }
      return false;
    } catch (e) {
      LoggerService.error('Error downloading SCORM module', e);
      return false;
    }
  }

  /// Show offline error dialog
  static void _showOfflineError(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Offline'),
        content: const Text('You are offline and this content is not available for offline viewing.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
