import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../theme/app_theme_extensions.dart';
import '../utils/responsive_utils.dart';
import '../config/app_config.dart';
import 'optimized_image_widget.dart';

/// A universal card widget that can be used for both course and library items
/// with customizable styling and functionality.
class UniversalListCard extends StatelessWidget {
  // Required properties
  final String thumbnailUrl;
  final String title;
  final String? description;
  final VoidCallback onTap;

  // Optional properties for customization
  final bool isHorizontal;
  final Widget? leadingBadge;
  final Widget? trailingBadge;
  final Widget? bottomContent;
  final Widget? actionIndicator;
  final Color? accentColor;
  final bool useFullBleedImage;
  final VoidCallback? onLongPress;
  final String? tooltipMessage;
  final double? customImageWidth;
  final double? customImageHeight;
  final BoxFit imageFit;
  final String? placeholderImageUrl;
  final bool applyImageGradient;
  final bool showBorder;
  final Color? borderColor;
  final Widget? overlayContent;
  final EdgeInsets? contentPadding;
  final bool optimizeImageUrl;
  final int? optimizedImageWidth;
  final int? optimizedImageHeight;
  final bool preserveImageRatio;

  const UniversalListCard({
    super.key,
    required this.thumbnailUrl,
    required this.title,
    required this.onTap,
    this.description,
    this.isHorizontal = true,
    this.leadingBadge,
    this.trailingBadge,
    this.bottomContent,
    this.actionIndicator,
    this.accentColor,
    this.useFullBleedImage = false,
    this.onLongPress,
    this.tooltipMessage,
    this.customImageWidth,
    this.customImageHeight,
    this.imageFit = BoxFit.cover,
    this.placeholderImageUrl,
    this.applyImageGradient = false,
    this.showBorder = false,
    this.borderColor,
    this.overlayContent,
    this.contentPadding,
    this.optimizeImageUrl = false,
    this.optimizedImageWidth,
    this.optimizedImageHeight,
    this.preserveImageRatio = true,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Get device type for responsive design
        final deviceType = ResponsiveUtils.getDeviceType(context);

        // Adjust card properties based on device type
        final double elevation = deviceType == DeviceType.mobile
            ? AppTheme.elevationMedium
            : AppTheme.elevationLarge;

        final double borderRadius = deviceType == DeviceType.mobile
            ? AppTheme.borderRadiusLarge
            : AppTheme.borderRadiusXLarge;

        final double titleFontSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.fontSizeMedium, // Increased from fontSizeSmall
          tablet: AppTheme.fontSizeLarge, // Increased from fontSizeMedium
          desktop: AppTheme.fontSizeXLarge, // Increased from fontSizeLarge
        );

        final double descriptionFontSize =
            ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.fontSizeSmall, // Increased from fontSizeSmall - 1
          tablet: AppTheme.fontSizeRegular, // Increased from fontSizeSmall
          desktop: AppTheme.fontSizeMedium, // Increased from fontSizeRegular
        );

        final EdgeInsets padding = contentPadding ??
            ResponsiveUtils.getValueForDeviceType(
              context: context,
              mobile: const EdgeInsets.all(
                  AppTheme.spacingSmall), // Increased from spacingXSmall
              tablet: const EdgeInsets.all(
                  AppTheme.spacingMedium), // Increased from spacingSmall
              desktop: const EdgeInsets.all(
                  AppTheme.spacingLarge), // Increased from spacingMedium
            );

        // Calculate image dimensions
        final double imageWidth = customImageWidth ??
            ResponsiveUtils.getValueForDeviceType(
              context: context,
              mobile: 120.0, // Increased from 80.0
              tablet: 150.0, // Increased from 100.0
              desktop: 180.0, // Increased from 120.0
            );

        final double imageHeight = customImageHeight ??
            ResponsiveUtils.getValueForDeviceType(
              context: context,
              mobile: 90.0, // Increased from 60.0
              tablet: 120.0, // Increased from 80.0
              desktop: 150.0, // Increased from 100.0
            );

        // Build the card content based on orientation
        Widget cardContent;

        if (isHorizontal) {
          cardContent = _buildHorizontalCard(
            context,
            borderRadius,
            padding,
            imageWidth,
            imageHeight,
            titleFontSize,
            descriptionFontSize,
          );
        } else {
          cardContent = _buildVerticalCard(
            context,
            borderRadius,
            titleFontSize,
            descriptionFontSize,
          );
        }

        // Create the card with appropriate styling
        final Widget card = Card(
          elevation: elevation + 1.0, // Increased elevation
          margin: const EdgeInsets.symmetric(
              vertical: 8, horizontal: 4), // Increased margin
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
            side: showBorder
                ? BorderSide(
                    color: borderColor ??
                        accentColor ??
                        Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.3),
                    width: 1,
                  )
                : BorderSide.none,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              onLongPress: onLongPress,
              borderRadius: BorderRadius.circular(borderRadius),
              child: cardContent,
            ),
          ),
        );

        // Add tooltip if provided
        if (tooltipMessage != null) {
          return Tooltip(
            message: tooltipMessage!,
            child: card,
          );
        }

        return card;
      },
    );
  }

  // Build horizontal card layout (image on left, content on right)
  Widget _buildHorizontalCard(
    BuildContext context,
    double borderRadius,
    EdgeInsets padding,
    double imageWidth,
    double imageHeight,
    double titleFontSize,
    double descriptionFontSize,
  ) {
    return Padding(
      padding: padding,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Thumbnail image
          ClipRRect(
            borderRadius: BorderRadius.circular(borderRadius / 2),
            child: SizedBox(
              width: imageWidth,
              height: imageHeight,
              child: _buildThumbnailImage(context),
            ),
          ),
          const SizedBox(width: AppTheme.spacingMedium),

          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title row with optional leading badge
                Row(
                  children: [
                    if (leadingBadge != null) ...[
                      leadingBadge!,
                      const SizedBox(width: AppTheme.spacingXSmall),
                    ],
                    Expanded(
                      child: _buildAutoSizingTitle(
                        context,
                        title,
                        titleFontSize,
                        isHorizontal: true,
                      ),
                    ),
                    if (trailingBadge != null) trailingBadge!,
                  ],
                ),

                if (description != null) ...[
                  const SizedBox(
                      height: AppTheme
                          .spacingSmall), // Increased from spacingXSmall
                  Text(
                    description!,
                    style: Theme.of(context).cardSubtitle.copyWith(
                          fontSize: descriptionFontSize,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.7),
                        ),
                    maxLines: 3, // Increased from 2 to allow more text
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                if (bottomContent != null) ...[
                  const SizedBox(
                      height: AppTheme
                          .spacingMedium), // Increased from spacingSmall
                  bottomContent!,
                ],

                if (actionIndicator != null) ...[
                  const SizedBox(
                      height: AppTheme
                          .spacingMedium), // Increased from spacingSmall
                  actionIndicator!,
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build vertical card layout (image on top, content below)
  Widget _buildVerticalCard(
    BuildContext context,
    double borderRadius,
    double titleFontSize,
    double descriptionFontSize,
  ) {
    return Stack(
      fit: StackFit.expand,
      children: [
        // Full-bleed thumbnail image
        ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius),
          child: _buildThumbnailImage(context),
        ),

        // Gradient overlay for better text readability
        if (applyImageGradient)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(borderRadius),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.3),
                    Colors.black.withValues(alpha: 0.8),
                  ],
                  stops: const [0.3, 0.6, 1.0],
                ),
              ),
            ),
          ),

        // Custom overlay content if provided
        if (overlayContent != null) overlayContent!,

        // Default content at the bottom
        if (overlayContent == null)
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(
                  AppTheme.spacingSmall,
                  AppTheme.spacingMedium,
                  AppTheme.spacingSmall,
                  AppTheme.spacingSmall),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildAutoSizingTitle(
                    context,
                    title,
                    titleFontSize,
                    isHorizontal: false,
                    textColor: Colors.white,
                    shadows: [
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 4,
                        color: Colors.black.withValues(alpha: 0.9),
                      ),
                      const Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black,
                      ),
                    ],
                  ),
                  if (description != null) ...[
                    const SizedBox(height: AppTheme.spacingXSmall),
                    Text(
                      description!,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: Colors.white,
                        fontSize: descriptionFontSize,
                        shadows: [
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black.withValues(alpha: 0.9),
                          ),
                          const Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 2,
                            color: Colors.black,
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  if (actionIndicator != null) ...[
                    const SizedBox(height: AppTheme.spacingSmall),
                    actionIndicator!,
                  ],
                ],
              ),
            ),
          ),
      ],
    );
  }

  // Build the thumbnail image with optimized loading and local placeholders
  Widget _buildThumbnailImage(BuildContext context) {
    // Determine the optimized image URL
    String imageUrl = thumbnailUrl;

    // Apply image optimization if requested
    if (optimizeImageUrl &&
        optimizedImageWidth != null &&
        optimizedImageHeight != null) {
      if (thumbnailUrl.isNotEmpty) {
        try {
          // Add width and height parameters for optimization
          String baseUrl = thumbnailUrl.startsWith('http')
              ? thumbnailUrl
              : '${AppConfig.apiBaseUrl}$thumbnailUrl';

          if (baseUrl.contains('?')) {
            imageUrl = '$baseUrl&width=$optimizedImageWidth';
            if (!preserveImageRatio) {
              imageUrl = '$imageUrl&height=$optimizedImageHeight';
            }
          } else {
            imageUrl = '$baseUrl?width=$optimizedImageWidth';
            if (!preserveImageRatio) {
              imageUrl = '$imageUrl&height=$optimizedImageHeight';
            }
          }
        } catch (e) {
          // Fallback to original URL if optimization fails
          imageUrl = thumbnailUrl;
        }
      }
    }

    // Use the new optimized image widget for better performance
    return CardThumbnailWidget(
      imageUrl: imageUrl,
      fit: imageFit,
      isVertical: !isHorizontal,
    );
  }

  // Build auto-sizing title that adjusts font size to fit content
  Widget _buildAutoSizingTitle(
    BuildContext context,
    String text,
    double baseFontSize, {
    required bool isHorizontal,
    Color? textColor,
    List<Shadow>? shadows,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Start with base font size and reduce if needed
        double fontSize = baseFontSize;
        int maxLines =
            isHorizontal ? 2 : 3; // Allow more lines for better readability

        // Create text style
        TextStyle baseStyle = isHorizontal
            ? Theme.of(context).cardTitle.copyWith(
                  fontSize: fontSize,
                  color: textColor,
                  shadows: shadows,
                )
            : Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: textColor ?? Colors.white,
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  shadows: shadows,
                );

        // Use a simple approach: try smaller font sizes if text is too long
        if (text.length > 30) {
          fontSize = baseFontSize * 0.85; // Reduce by 15%
        }
        if (text.length > 50) {
          fontSize = baseFontSize * 0.75; // Reduce by 25%
        }
        if (text.length > 70) {
          fontSize = baseFontSize * 0.65; // Reduce by 35%
        }

        // Update style with adjusted font size
        TextStyle finalStyle = baseStyle.copyWith(fontSize: fontSize);

        return Text(
          text,
          style: finalStyle,
          maxLines: maxLines,
          overflow: TextOverflow.ellipsis,
        );
      },
    );
  }
}
