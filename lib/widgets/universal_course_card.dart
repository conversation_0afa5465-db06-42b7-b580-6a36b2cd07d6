import 'package:flutter/material.dart';
import '../models/course_model.dart';
import '../theme/app_theme.dart';
import '../config/app_config.dart';
import 'universal_list_card.dart';

/// A specialized implementation of UniversalListCard for courses
class UniversalCourseCard extends StatelessWidget {
  final Course course;
  final VoidCallback onTap;
  final bool isHorizontal;
  final bool? isDownloaded;
  final double? progress;

  const UniversalCourseCard({
    Key? key,
    required this.course,
    required this.onTap,
    this.isHorizontal = true,
    this.isDownloaded,
    this.progress,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Build status indicators for downloaded courses and progress
    Widget? statusIndicator;
    if ((isDownloaded == true) || progress != null) {
      statusIndicator = Row(
        children: [
          if (isDownloaded == true)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingMedium,  // Increased from spacingSmall
                vertical: AppTheme.spacingXSmall,    // Increased from spacingXSmall / 2
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.download_done,
                    size: 16,  // Increased from 14
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 6),  // Increased from 4
                  Text(
                    'Downloaded',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSmall,  // Increased from fontSizeSmall - 1
                      fontWeight: FontWeight.bold,  // Added bold
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),

          if (isDownloaded == true && progress != null)
            const SizedBox(width: AppTheme.spacingSmall),

          if (progress != null)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${progress!.toStringAsFixed(0)}% Complete',
                    style: TextStyle(
                      fontSize: AppTheme.fontSizeSmall,  // Increased from fontSizeSmall - 1
                      fontWeight: FontWeight.bold,  // Added bold
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 4),  // Increased from 2
                  SizedBox(
                    height: 6.0,  // Set a specific height for the progress indicator
                    child: LinearProgressIndicator(
                      value: progress! / 100,
                      backgroundColor: theme.colorScheme.surfaceVariant,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.primary,
                      ),
                      borderRadius: BorderRadius.circular(3.0),  // Add rounded corners
                    ),
                  ),
                ],
              ),
            ),
        ],
      );
    }

    return UniversalListCard(
      thumbnailUrl: course.banner,
      title: course.title,
      // Only show description in horizontal mode
      description: isHorizontal ? course.description : null,
      onTap: onTap,
      isHorizontal: isHorizontal,
      bottomContent: statusIndicator,
      applyImageGradient: !isHorizontal,
      placeholderImageUrl: AppConfig.placeholderCoverImage,
      showBorder: isDownloaded == true,
      borderColor: isDownloaded == true
          ? theme.colorScheme.primary.withOpacity(0.3)
          : null,
    );
  }
}
