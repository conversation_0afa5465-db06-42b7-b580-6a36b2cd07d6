import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';
import '../theme/app_theme.dart';

/// A widget that builds different layouts based on screen size
class ResponsiveBuilder extends StatelessWidget {
  /// Builder function for mobile layout
  final Widget Function(BuildContext context) mobileBuilder;

  /// Builder function for tablet layout (optional)
  final Widget Function(BuildContext context)? tabletBuilder;

  /// Builder function for desktop layout (optional)
  final Widget Function(BuildContext context)? desktopBuilder;

  /// Constructor
  const ResponsiveBuilder({
    Key? key,
    required this.mobileBuilder,
    this.tabletBuilder,
    this.desktopBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final deviceType = ResponsiveUtils.getDeviceType(context);

        switch (deviceType) {
          case DeviceType.mobile:
            return mobileBuilder(context);
          case DeviceType.tablet:
            return tabletBuilder != null
                ? tabletBuilder!(context)
                : mobileBuilder(context);
          case DeviceType.desktop:
            return desktopBuilder != null
                ? desktopBuilder!(context)
                : (tabletBuilder != null
                    ? tabletBuilder!(context)
                    : mobileBuilder(context));
        }
      },
    );
  }
}

/// A widget that provides different layouts based on screen orientation
class OrientationResponsiveBuilder extends StatelessWidget {
  /// Builder function for portrait layout
  final Widget Function(BuildContext context) portraitBuilder;

  /// Builder function for landscape layout
  final Widget Function(BuildContext context) landscapeBuilder;

  /// Constructor
  const OrientationResponsiveBuilder({
    Key? key,
    required this.portraitBuilder,
    required this.landscapeBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        if (orientation == Orientation.portrait) {
          return portraitBuilder(context);
        } else {
          return landscapeBuilder(context);
        }
      },
    );
  }
}

/// A widget that provides a responsive grid layout
class ResponsiveGridView extends StatelessWidget {
  /// List of items to display in the grid
  final List<Widget> children;

  /// Number of columns for mobile layout
  final int? mobileColumns;

  /// Number of columns for tablet layout
  final int? tabletColumns;

  /// Number of columns for desktop layout
  final int? desktopColumns;

  /// Spacing between items
  final double spacing;

  /// Child aspect ratio
  final double? childAspectRatio;

  /// Whether the grid should be scrollable
  final bool scrollable;

  /// Whether the grid should shrink wrap its contents
  final bool shrinkWrap;

  /// Constructor
  const ResponsiveGridView({
    Key? key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.spacing = 16.0,
    this.childAspectRatio,
    this.scrollable = true,
    this.shrinkWrap = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = ResponsiveUtils.getResponsiveGridCount(
      context: context,
      mobile: mobileColumns,
      tablet: tabletColumns,
      desktop: desktopColumns,
    );

    final aspectRatio = childAspectRatio ?? AppTheme.cardAspectRatio;

    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: aspectRatio,
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
      shrinkWrap: shrinkWrap,
      physics: scrollable
          ? const AlwaysScrollableScrollPhysics()
          : const NeverScrollableScrollPhysics(),
    );
  }
}

/// A widget that provides responsive padding
class ResponsivePadding extends StatelessWidget {
  /// Child widget
  final Widget child;

  /// Padding for mobile layout
  final EdgeInsets? mobilePadding;

  /// Padding for tablet layout
  final EdgeInsets? tabletPadding;

  /// Padding for desktop layout
  final EdgeInsets? desktopPadding;

  /// Constructor
  const ResponsivePadding({
    Key? key,
    required this.child,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final padding = ResponsiveUtils.getResponsivePadding(
      context: context,
      mobile: mobilePadding,
      tablet: tabletPadding,
      desktop: desktopPadding,
    );

    return Padding(
      padding: padding,
      child: child,
    );
  }
}
