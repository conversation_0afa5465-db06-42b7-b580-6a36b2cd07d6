import 'package:flutter/material.dart';
import '../localization/app_localizations_extension.dart';
import '../utils/responsive_utils.dart';
import '../theme/app_theme.dart';
import 'optimized_image_widget.dart';

class FeatureCard extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String description;
  final VoidCallback onExplore;

  const FeatureCard({
    required this.imageUrl,
    required this.title,
    required this.description,
    required this.onExplore,
  });

  @override
  Widget build(BuildContext context) {
    // Get theme colors
    final Color primaryColor = Theme.of(context).primaryColor;
    final Color cardColor = Theme.of(context).cardColor;
    final Color textColor =
        Theme.of(context).textTheme.titleMedium?.color ?? Colors.black87;
    final Color subtitleColor =
        Theme.of(context).textTheme.bodySmall?.color ?? Colors.black54;

    return Container(
      margin: const EdgeInsets.only(bottom: 10.0),
      child: Material(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.0),
        child: InkWell(
          onTap: onExplore,
          borderRadius: BorderRadius.circular(12.0),
          child: Ink(
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.circular(12.0),
              border: Border.all(
                color: Theme.of(context).dividerColor.withOpacity(0.5),
                width: 1.0,
              ),
            ),
            child: Row(
              children: [
                // Left accent color
                Container(
                  width: 6,
                  height: 110,
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12.0),
                      bottomLeft: Radius.circular(12.0),
                    ),
                  ),
                ),

                // Icon
                Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 12.0, 12.0, 12.0),
                  child: SizedBox(
                    width: 64,
                    height: 64,
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.image,
                          size: 40,
                          color: primaryColor,
                        );
                      },
                    ),
                  ),
                ),

                // Content
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(0, 12.0, 12.0, 12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4.0),
                        Text(
                          description,
                          style: TextStyle(
                            fontSize: 12,
                            color: subtitleColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 6.0),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              context.tr('common.explore'),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: primaryColor,
                              ),
                            ),
                            const SizedBox(width: 4.0),
                            Icon(
                              Icons.arrow_forward_rounded,
                              size: 14,
                              color: primaryColor,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
