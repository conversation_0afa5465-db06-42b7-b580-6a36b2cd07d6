import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../config/app_config.dart';

/// An optimized image widget that provides instant local placeholders
/// and smooth background loading for better UI performance
class OptimizedImageWidget extends StatelessWidget {
  final String imageUrl;
  final BoxFit fit;
  final double? width;
  final double? height;
  final String? localPlaceholderAsset;
  final Widget? customPlaceholder;
  final Color? placeholderColor;
  final Duration fadeDuration;
  final int? memCacheWidth;
  final int? memCacheHeight;

  const OptimizedImageWidget({
    Key? key,
    required this.imageUrl,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.localPlaceholderAsset,
    this.customPlaceholder,
    this.placeholderColor,
    this.fadeDuration = const Duration(milliseconds: 300),
    this.memCacheWidth = 400,
    this.memCacheHeight = 400,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // If no image URL provided, show placeholder only
    if (imageUrl.isEmpty) {
      return _buildLocalPlaceholder(context);
    }

    // Determine the full image URL
    String fullImageUrl = imageUrl;
    if (!imageUrl.startsWith('http')) {
      fullImageUrl = '${AppConfig.apiBaseUrl}$imageUrl';
    }

    return CachedNetworkImage(
      imageUrl: fullImageUrl,
      fit: fit,
      width: width,
      height: height,
      memCacheWidth: memCacheWidth,
      memCacheHeight: memCacheHeight,
      fadeInDuration: fadeDuration,
      fadeOutDuration: fadeDuration,
      placeholder: (context, url) => _buildLocalPlaceholder(context),
      errorWidget: (context, url, error) => _buildLocalPlaceholder(context),
      imageBuilder: (context, imageProvider) => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: fit,
          ),
        ),
      ),
    );
  }

  /// Build a local placeholder that displays immediately
  Widget _buildLocalPlaceholder(BuildContext context) {
    // If a custom placeholder is provided, use it
    if (customPlaceholder != null) {
      return customPlaceholder!;
    }

    // If a local asset is specified, use it
    if (localPlaceholderAsset != null) {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: placeholderColor ?? Theme.of(context).colorScheme.surfaceVariant,
        ),
        child: Image.asset(
          localPlaceholderAsset!,
          fit: fit,
          width: width,
          height: height,
          errorBuilder: (context, error, stackTrace) => _buildIconPlaceholder(context),
        ),
      );
    }

    // Default to icon placeholder
    return _buildIconPlaceholder(context);
  }

  /// Build a simple icon placeholder
  Widget _buildIconPlaceholder(BuildContext context) {
    return Container(
      width: width,
      height: height,
      color: placeholderColor ?? Theme.of(context).colorScheme.surfaceVariant,
      child: Icon(
        Icons.image,
        size: (width != null && height != null) 
            ? (width! < height! ? width! * 0.3 : height! * 0.3)
            : 40,
        color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
      ),
    );
  }
}

/// Specialized widget for course and library card thumbnails
class CardThumbnailWidget extends StatelessWidget {
  final String imageUrl;
  final BoxFit fit;
  final double? width;
  final double? height;
  final bool isVertical;

  const CardThumbnailWidget({
    Key? key,
    required this.imageUrl,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.isVertical = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OptimizedImageWidget(
      imageUrl: imageUrl,
      fit: fit,
      width: width,
      height: height,
      localPlaceholderAsset: 'assets/images/placeholder.png',
      fadeDuration: const Duration(milliseconds: 200),
      memCacheWidth: 300, // Smaller cache for thumbnails
      memCacheHeight: 300,
    );
  }
}

/// Specialized widget for profile images
class ProfileImageWidget extends StatelessWidget {
  final String imageUrl;
  final double size;
  final BoxFit fit;

  const ProfileImageWidget({
    Key? key,
    required this.imageUrl,
    this.size = 50,
    this.fit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OptimizedImageWidget(
      imageUrl: imageUrl,
      fit: fit,
      width: size,
      height: size,
      localPlaceholderAsset: 'assets/images/profile.png',
      fadeDuration: const Duration(milliseconds: 150),
      memCacheWidth: size.toInt(),
      memCacheHeight: size.toInt(),
    );
  }
}
