import 'package:flutter/material.dart';
import '../models/library_item_model.dart';
import '../theme/app_theme.dart';
import '../theme/app_theme_extensions.dart';
import '../localization/app_localizations_extension.dart';
import 'universal_list_card.dart';

/// A specialized implementation of UniversalListCard for library items
class UniversalLibraryCard extends StatelessWidget {
  final LibraryItem item;
  final Function(LibraryItem) onDownload;
  final Function(LibraryItem) onOpen;
  final Function(LibraryItem)? onDelete;
  final bool isHorizontal;

  const UniversalLibraryCard({
    super.key,
    required this.item,
    required this.onDownload,
    required this.onOpen,
    this.onDelete,
    this.isHorizontal = true,
  });

  // Get color based on content type
  Color _getContentTypeColor(BuildContext context) {
    return Theme.of(context).getContentTypeColor(item.contentType);
  }

  // Get icon based on content type
  IconData _getContentTypeIcon() {
    switch (item.contentType.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'video':
      case 'mp4':
        return Icons.video_library;
      case 'audio':
      case 'mp3':
        return Icons.audiotrack;
      case 'link':
        return Icons.link;
      default:
        return Icons.insert_drive_file;
    }
  }

  // Get the appropriate action text based on item state
  String _getActionText(BuildContext context) {
    if (item.isDownloaded) {
      return context.tr('library.open');
    } else if (item.isExternalLink) {
      return context.tr('library.open_link');
    } else {
      return context.tr('library.download');
    }
  }

  // Get the appropriate action icon based on item state
  IconData _getActionIcon() {
    if (item.isDownloaded) {
      return Icons.open_in_new;
    } else if (item.isExternalLink) {
      return Icons.link;
    } else {
      return Icons.download;
    }
  }

  // Handle tap based on item state
  void _handleTap(BuildContext context) {
    if (item.isDownloaded || item.isExternalLink) {
      onOpen(item);
    } else {
      onDownload(item);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final contentTypeColor = _getContentTypeColor(context);
    final contentTypeIcon = _getContentTypeIcon();

    // Create content type badge
    Widget contentTypeBadge = Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: contentTypeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
      ),
      child: Icon(
        contentTypeIcon,
        size: 14,
        color: contentTypeColor,
      ),
    );

    // Create action indicator - only show for downloaded or external link items, not for download action
    Widget? actionIndicator;
    if (item.isDownloaded || item.isExternalLink) {
      actionIndicator = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getActionIcon(),
            size: isHorizontal ? 14 : 12,
            color: isHorizontal ? Colors.white : Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            _getActionText(context),
            style: isHorizontal
                ? theme.primaryButtonText.copyWith(
                    fontSize: AppTheme.fontSizeSmall - 1,
                  )
                : theme.smallText.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    shadows: [
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black.withValues(alpha: 0.9),
                      ),
                      const Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 2,
                        color: Colors.black,
                      ),
                    ],
                  ),
          ),
        ],
      );
    }

    return UniversalListCard(
      thumbnailUrl: item.contentThumbnail,
      title: item.contentTitle,
      // Only show description in horizontal mode
      description: isHorizontal ? item.contentSummary : null,
      onTap: () => _handleTap(context),
      onLongPress:
          item.isDownloaded && onDelete != null ? () => onDelete!(item) : null,
      isHorizontal: isHorizontal,
      leadingBadge: isHorizontal ? contentTypeBadge : null,
      actionIndicator: actionIndicator,
      accentColor: contentTypeColor,
      applyImageGradient: !isHorizontal,
      tooltipMessage: item.isDownloaded && onDelete != null
          ? context.tr('library.long_press_to_delete')
          : null,
      showBorder: item.isDownloaded,
      borderColor: item.isDownloaded
          ? AppTheme.successColor.withValues(alpha: 0.3)
          : null,
      optimizeImageUrl: true,
      optimizedImageWidth: isHorizontal ? 160 : 280,
      optimizedImageHeight: isHorizontal ? 120 : 350,
      preserveImageRatio: isHorizontal,
    );
  }
}
