import 'package:flutter/material.dart';
import '../../services/html_parser_service.dart';

class UserExperienceCard extends StatefulWidget {
  final Map<String, dynamic>? experiences;
  final bool isTablet;
  final double cardPadding;

  const UserExperienceCard({
    super.key,
    required this.experiences,
    this.isTablet = false,
    this.cardPadding = 12, // reduced padding
  });

  @override
  State<UserExperienceCard> createState() => _UserExperienceCardState();
}

class _UserExperienceCardState extends State<UserExperienceCard> {
  final HtmlParserService parser = HtmlParserService();
  final Map<String, bool> _expanded = {};

  @override
  void initState() {
    super.initState();
    if (widget.experiences != null) {
      for (var key in widget.experiences!.keys) {
        _expanded[key] = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final experiences = widget.experiences;

    return Padding(
      padding: EdgeInsets.only(
        top: widget.isTablet ? 32 : 24,
        bottom: widget.isTablet ? 8 : 4,
        left: widget.isTablet ? 32.0 : 0,
        right: widget.isTablet ? 32.0 : 0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (experiences != null && experiences.isNotEmpty) ...[
            // Section Title
            Text(
              'Experiences',
              style: TextStyle(
                fontSize: widget.isTablet ? 20 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
          ],

          if (experiences != null && experiences.isNotEmpty)
            ...experiences.entries.map((entry) {
              final parsed = parser.parse(entry.value ?? '');
              final isOpen = _expanded[entry.key] ?? false;
              final firstParagraph =
              parsed.paragraphs.isNotEmpty ? parsed.paragraphs.first : '';

              return Container(
                margin: const EdgeInsets.only(bottom: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 8,
                      spreadRadius: 2,
                      offset: const Offset(2, 4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(
                          entry.key,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: widget.isTablet ? 18 : 16,
                          ),
                        ),
                        subtitle: !isOpen && firstParagraph.isNotEmpty
                            ? Padding(
                          padding: const EdgeInsets.only(top: 6.0),
                          child: Text(
                            firstParagraph,
                            style: TextStyle(
                                fontSize: widget.isTablet ? 16 : 14),
                          ),
                        )
                            : null,
                        trailing: IconButton(
                          icon: Icon(
                            isOpen ? Icons.expand_less : Icons.expand_more,
                            size: widget.isTablet ? 28 : 24,
                          ),
                          onPressed: () {
                            setState(() {
                              _expanded[entry.key] = !isOpen;
                            });
                          },
                        ),
                        dense: !widget.isTablet,
                      ),
                      if (isOpen)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: _buildExperienceContent(parsed),
                        ),
                    ],
                  ),
                ),
              );
            }),
        ],
      ),
    );
  }

  Widget _buildExperienceContent(ParsedHtmlContent content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: content.orderedElements.map((element) {
        switch (element) {
          case ParagraphElement():
            return Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child: Text(
                (element as ParagraphElement).text,
                style: TextStyle(fontSize: widget.isTablet ? 16 : 14),
              ),
            );
          case ListItemElement():
            return Padding(
              padding: const EdgeInsets.only(left: 8, bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("• "),
                  Expanded(
                    child: Text(
                      (element as ListItemElement).text,
                      style: TextStyle(fontSize: widget.isTablet ? 16 : 14),
                    ),
                  ),
                ],
              ),
            );
          case HrElement():
            return const Divider(thickness: 1);
          case ImageElement():
            final img = (element as ImageElement).image;
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.memory(img.bytes),
              ),
            );
          default:
            return const SizedBox();
        }
      }).toList(),
    );
  }
}
