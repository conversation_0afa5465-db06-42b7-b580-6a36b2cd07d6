import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/html_parser_service.dart';
import '../services/logger_service.dart';
import '../theme/app_theme.dart';

/// Widget to display parsed HTML content with proper styling
class HtmlContentWidget extends StatelessWidget {
  final String htmlContent;
  final TextStyle? textStyle;
  final Color? linkColor;
  final double? lineHeight;
  final EdgeInsets? padding;
  final bool showImages;

  const HtmlContentWidget({
    Key? key,
    required this.htmlContent,
    this.textStyle,
    this.linkColor,
    this.lineHeight,
    this.padding,
    this.showImages = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (htmlContent.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    final htmlParser = HtmlParserService();
    final parsedContent = htmlParser.parse(htmlContent);

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Use ordered elements to maintain original document structure
          ...parsedContent.orderedElements
              .map((element) => _buildOrderedElement(
                    context,
                    element,
                  )),
        ],
      ),
    );
  }

  /// Build widget for ordered HTML elements
  Widget _buildOrderedElement(BuildContext context, HtmlElement element) {
    switch (element.type) {
      case 'paragraph':
        final paragraphElement = element as ParagraphElement;
        return _buildParagraph(context, paragraphElement.text);
      case 'listItem':
        final listItemElement = element as ListItemElement;
        return _buildListItem(context, listItemElement.text);
      case 'link':
        final linkElement = element as LinkElement;
        return _buildLink(context, linkElement.link);
      case 'image':
        if (showImages) {
          final imageElement = element as ImageElement;
          return _buildImage(context, imageElement.image);
        }
        return const SizedBox.shrink();
      case 'hr':
        return _buildHorizontalRule(context);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildHorizontalRule(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMedium),
      child: Container(
        height: 1,
        width: double.infinity,
        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
      ),
    );
  }

  Widget _buildParagraph(BuildContext context, String paragraph) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingSmall),
      child: Text(
        paragraph,
        style: textStyle ??
            Theme.of(context).textTheme.bodyLarge?.copyWith(
                  height: lineHeight ?? 1.6,
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.8),
                ),
      ),
    );
  }

  Widget _buildListItem(BuildContext context, String listItem) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: AppTheme.spacingXSmall,
        left: AppTheme.spacingMedium,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(
              top: 8,
              right: AppTheme.spacingSmall,
            ),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .onSurface
                  .withValues(alpha: 0.6),
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              listItem,
              style: textStyle ??
                  Theme.of(context).textTheme.bodyLarge?.copyWith(
                        height: lineHeight ?? 1.6,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.8),
                      ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLink(BuildContext context, LinkItem link) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingXSmall),
      child: InkWell(
        onTap: () => _launchUrl(link.href),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: AppTheme.spacingXSmall,
            horizontal: AppTheme.spacingSmall,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.link,
                size: 16,
                color: linkColor ?? Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: AppTheme.spacingXSmall),
              Flexible(
                child: Text(
                  link.text.isNotEmpty ? link.text : link.href,
                  style: textStyle?.copyWith(
                        color:
                            linkColor ?? Theme.of(context).colorScheme.primary,
                        decoration: TextDecoration.underline,
                      ) ??
                      Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: linkColor ??
                                Theme.of(context).colorScheme.primary,
                            decoration: TextDecoration.underline,
                            height: lineHeight ?? 1.6,
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImage(BuildContext context, Base64ImageItem image) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
      child: Container(
        constraints: const BoxConstraints(
          maxHeight: 300,
          maxWidth: double.infinity,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          boxShadow: [
            BoxShadow(
              color:
                  Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          child: Image.memory(
            image.bytes,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              LoggerService.error('Error displaying base64 image', error);
              return Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest
                      .withValues(alpha: 0.3),
                  borderRadius:
                      BorderRadius.circular(AppTheme.borderRadiusMedium),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.5),
                      ),
                      const SizedBox(height: AppTheme.spacingXSmall),
                      Text(
                        'Image could not be displayed',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withValues(alpha: 0.5),
                            ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        LoggerService.warning('Could not launch URL: $url');
      }
    } catch (e) {
      LoggerService.error('Error launching URL: $url', e);
    }
  }
}
