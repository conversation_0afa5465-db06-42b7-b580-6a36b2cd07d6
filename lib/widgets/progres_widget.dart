import 'package:ZABAI/localization/app_localizations_extension.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/app_config.dart';
import '../models/course_model.dart';
import '../screens/library/pdf_viewer_screen.dart';

class UserProgressWidget extends StatefulWidget {
  final List<Course> allCourses;
  final List<EnrolledCourse> enrolledCourses;

  const UserProgressWidget({
    super.key,
    required this.allCourses,
    required this.enrolledCourses,
  });

  @override
  State<UserProgressWidget> createState() => _UserProgressWidgetState();
}

class _UserProgressWidgetState extends State<UserProgressWidget> {
  final Map<int, bool> _expanded = {};

  Future<void> openInBrowser(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication, // Opens in default browser
      );
    } else {
      throw 'Could not launch $url';
    }
  }

  @override
  @override
  Widget build(BuildContext context) {
    final filteredCourses = widget.enrolledCourses.where((enrolled) {
      try {
        final course = widget.allCourses.firstWhere((c) => c.id == enrolled.id);
        final totalModules = course.modules.length;
        final completedModules = enrolled.modules.where((m) => m.completionStatus).length;
        final progress = totalModules > 0 ? completedModules / totalModules : 0;
        return progress > 0;
      } catch (_) {
        return false;
      }
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (filteredCourses.isNotEmpty)
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Text(
              context.tr('profile.progress_title'),
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).textTheme.bodyLarge!.color,
              ),
            ),
          ),

        ListView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: filteredCourses.map((enrolled) {
            final course = widget.allCourses.firstWhere((c) => c.id == enrolled.id);
            final double completionRate = course.modules.isNotEmpty
                ? (enrolled.modules.where((m) => m.completionStatus).length / course.modules.length)
                : 0;

            final isExpanded = _expanded[course.id] ?? false;
            final enrolledModuleMap = {
              for (var m in enrolled.modules) m.id: m.completionStatus
            };

            return GestureDetector(
              onTap: () {
                setState(() {
                  _expanded[course.id] = !isExpanded;
                });
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 8,
                      spreadRadius: 2,
                      offset: const Offset(2, 4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              "${AppConfig.apiBaseUrl}/${course.banner}",
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                              errorBuilder: (_, __, ___) => Container(
                                width: 80,
                                height: 80,
                                color: Colors.grey[300],
                                child: const Icon(Icons.broken_image),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  course.title,
                                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                LinearProgressIndicator(
                                  value: completionRate,
                                  backgroundColor: Colors.grey[300],
                                  color: completionRate == 1.0
                                      ? Colors.green
                                      : Colors.orangeAccent,
                                ),
                                const SizedBox(height: 6),
                                Text(
                                  "${(completionRate * 100).toStringAsFixed(0)}% ${context.tr("profile.completed")}",
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                          Icon(isExpanded ? Icons.expand_less : Icons.expand_more),
                        ],
                      ),

                      if (isExpanded) ...[
                        const Divider(height: 20),
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: course.modules.length,
                          itemBuilder: (context, index) {
                            final module = course.modules[index];
                            final completed = enrolledModuleMap[module.id] ?? false;

                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    completed ? Icons.check_circle : Icons.radio_button_unchecked,
                                    color: completed ? Colors.green : Colors.grey,
                                  ),
                                  const SizedBox(width: 10),
                                  Expanded(
                                    child: Text(
                                      module.name,
                                      style: TextStyle(
                                        fontWeight: completed ? FontWeight.bold : FontWeight.normal,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                        if (enrolled.certificate != null) ...[
                          const Divider(height: 24),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () {
                                final certId = enrolled.certificate!['id'];
                                openInBrowser("${AppConfig.apiBaseUrl}${AppConfig.certificateDownloadLink}/$certId");
                              },
                              icon: const Icon(Icons.download),
                              label: Text(context.tr("courses.certificate")),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 14),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
