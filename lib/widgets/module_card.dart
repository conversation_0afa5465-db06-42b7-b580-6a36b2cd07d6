import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/course_model.dart';
import '../screens/courses/download_screen.dart';
import '../screens/courses/simple_download_screen.dart';
import '../services/content_handler_service.dart';
import '../theme/app_theme.dart';
import '../theme/app_theme_extensions.dart';
import '../utils/responsive_utils.dart';
import 'package:ZABAI/localization/app_localizations_extension.dart';

import '../utils/scorm_player.dart';

class ModuleCard extends StatefulWidget {
  final Module module;
  final String courseSlug;
  final VoidCallback onWatch;
  final IconData iconData;
  final Color? iconColor;

  const ModuleCard({
    super.key,
    required this.module,
    required this.courseSlug,
    required this.onWatch,
    required this.iconData,
    required this.iconColor,
  });

  @override
  State<ModuleCard> createState() => _ModuleCardState();
}

class _ModuleCardState extends State<ModuleCard> {
  late bool _fileExists = false;

  @override
  void initState() {
    super.initState();
    _checkFileExistence();
  }

  // Memoized path calculation
  String get _modulePath {
    return '/my_courses/${widget.courseSlug}/${widget.module.moduleSlug}';
  }

  Future<void> _checkFileExistence() async {
    try {
      final externalDirectories = await getExternalStorageDirectories();
      bool exists = false;

      for (var externalDir in externalDirectories!) {
        final folderPath = '${externalDir.path}$_modulePath';
        if (await Directory(folderPath).exists()) {
          exists = true;
          break; // Exit early on first match
        }
      }
      if (mounted) {
        setState(() {
          _fileExists = exists;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking file existence: $e');
      }
      if (mounted) {
        setState(() {
          _fileExists = false;
        });
      }
    }
  }

  Future<void> _handleModuleTap() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    final isOnline = connectivityResult != ConnectivityResult.none;

    // Determine content type
    final isScorm = widget.module.isScormContent;
    final isPdf = widget.module.isPdfContent;
    final isAudioVideo = widget.module.isAudioVideoContent;

    if (isOnline) {
      if (_fileExists) {
        // Online & File Exists -> play offline version (downloaded content)
        await _playOfflineContent(isScorm, isPdf, isAudioVideo);
      } else {
        // Online & No File -> show bottom sheet with options
        await _showOnlineOptions(isScorm, isPdf, isAudioVideo);
      }
    } else {
      if (_fileExists) {
        // Offline & File Exists -> play offline version
        await _playOfflineContent(isScorm, isPdf, isAudioVideo);
      } else {
        // Offline & No File -> show error
        _showOfflineError();
      }
    }
  }

  Future<void> _playOfflineContent(bool isScorm, bool isPdf, bool isAudioVideo) async {
    if (isScorm) {
      // Play SCORM content offline
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ScormPlayer(
            scormDataPath: '${_modulePath.substring(1)}/${widget.module.scormDataPath}',
            moduleName: widget.module.name,
            moduleId: widget.module.id,
            courseId: widget.module.id,
          ),
        ),
      );
    } else {
      // For PDF/Audio/Video, use ContentHandlerService to open the downloaded file
      // Create a mock EnrolledModule to use with ContentHandlerService
      final mockModule = EnrolledModule(
        id: widget.module.id,
        completionStatus: false,
        lock: false,
        content: ModuleContent(
          type: widget.module.effectiveContentType,
          downloadLink: widget.module.downloadLink,
          scormDataPath: widget.module.scormDataPath,
        ),
      );

      await ContentHandlerService.openEnrolledModule(
        context,
        mockModule,
        courseSlug: widget.courseSlug,
      );
    }
  }

  Future<void> _showOnlineOptions(bool isScorm, bool isPdf, bool isAudioVideo) async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
      ),
      builder: (context) => SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.play_circle),
              title: Text(context.tr('courses.watch_online')),
              onTap: () {
                Navigator.pop(context);
                _watchOnline(isScorm);
              },
            ),
            ListTile(
              leading: const Icon(Icons.download),
              title: Text(context.tr('courses.download')),
              onTap: () {
                Navigator.pop(context);
                _downloadContent(isScorm);
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _watchOnline(bool isScorm) async {
    if (isScorm) {
      // For SCORM content, use the original onWatch callback (special SCORM API)
      widget.onWatch();
    } else {
      // For PDF/Audio/Video content, use openEnrolledModule for online viewing
      // Create a mock EnrolledModule to use with ContentHandlerService
      final mockModule = EnrolledModule(
        id: widget.module.id,
        completionStatus: false,
        lock: false,
        content: ModuleContent(
          type: widget.module.effectiveContentType,
          downloadLink: widget.module.downloadLink,
          scormDataPath: widget.module.scormDataPath,
        ),
      );

      await ContentHandlerService.openEnrolledModule(
        context,
        mockModule,
        courseSlug: widget.courseSlug,
      );
    }
  }

  void _downloadContent(bool isScorm) {
    if (isScorm) {
      // Use existing DownloadPage for SCORM content (with extraction)
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => DownloadPage(
            courseSlug: widget.courseSlug,
            moduleSlug: widget.module.moduleSlug,
            downloadLink: widget.module.downloadLink,
          ),
        ),
      ).then((_) => _checkFileExistence());
    } else {
      // Use SimpleDownloadPage for PDF/Audio/Video content (no extraction)
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SimpleDownloadPage(
            courseSlug: widget.courseSlug,
            moduleSlug: widget.module.moduleSlug,
            downloadLink: widget.module.downloadLink,
            contentType: widget.module.effectiveContentType,
          ),
        ),
      ).then((_) => _checkFileExistence());
    }
  }

  void _showOfflineError() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Offline'),
        content: const Text('You are offline and this content is not available.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final theme = Theme.of(context);
        final deviceType = ResponsiveUtils.getDeviceType(context);

        // Responsive values based on device type
        final double elevation = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.elevationSmall,
          tablet: AppTheme.elevationMedium,
          desktop: AppTheme.elevationMedium,
        );

        final double borderRadius = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.borderRadiusMedium,
          tablet: AppTheme.borderRadiusLarge,
          desktop: AppTheme.borderRadiusLarge,
        );

        final EdgeInsets padding = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: const EdgeInsets.all(12.0),
          tablet: const EdgeInsets.all(16.0),
          desktop: const EdgeInsets.all(20.0),
        );

        final double titleFontSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.fontSizeMedium,
          tablet: AppTheme.fontSizeLarge,
          desktop: AppTheme.fontSizeXLarge,
        );

        final double descriptionFontSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: AppTheme.fontSizeSmall,
          tablet: AppTheme.fontSizeRegular,
          desktop: AppTheme.fontSizeMedium,
        );

        final double iconSize = ResponsiveUtils.getValueForDeviceType(
          context: context,
          mobile: 20.0,
          tablet: 24.0,
          desktop: 28.0,
        );

        // Create a custom title style based on the responsive font size
        final TextStyle titleStyle = theme.cardTitle.copyWith(
          fontSize: titleFontSize,
        );

        // Create a custom subtitle style based on the responsive font size
        final TextStyle subtitleStyle = theme.cardSubtitle.copyWith(
          fontSize: descriptionFontSize,
        );

        return Card(
          margin: EdgeInsets.zero,
          elevation: elevation,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          child: InkWell(
            onTap: () async {
              await _handleModuleTap();
            },
            borderRadius: BorderRadius.circular(borderRadius),
            child: Padding(
              padding: padding,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Content section (title and description)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text("Content Type: ${widget.module.effectiveContentType}"),
                        // Module title
                        Text(
                          widget.module.name,
                          style: titleStyle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        // Module description (if available)
                        if (widget.module.description.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              widget.module.description,
                              style: subtitleStyle,
                              maxLines: deviceType == DeviceType.mobile ? 2 : 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Eye icon button
                  Container(
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusCircular),
                    ),
                    child: IconButton(
                      icon: Icon(
                        widget.iconData,
                        size: iconSize,
                        color: widget.iconColor, // ✅ explicitly set the color here
                      ),
                      onPressed: widget.onWatch,
                      tooltip: context.tr('courses.watch_online'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}


