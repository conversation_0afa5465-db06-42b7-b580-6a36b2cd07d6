import 'package:flutter/material.dart';

/// AppTheme class that centralizes all theme-related configurations
/// for the application. This makes it easier to maintain a consistent
/// look and feel across the entire app.
class AppTheme {
  // Define color constants for SCORE
  // static const Color primaryColor = Color(0xFFE53935); // Red 600
  // static const Color secondaryColor = Color(0xFFEF5350); // Red 400
  // static const Color accentColor = Color(0xFFFF8A80); // Red A100
  // static const Color backgroundColor = Colors.white;
  // static const Color cardColor = Colors.white;
  // static const Color errorColor = Color(0xFFB71C1C); // Red 900
  // static const Color successColor = Color(0xFF43A047); // Green 600
  // static const Color textPrimaryColor = Color(0xFF212121); // Grey 900
  // static const Color textSecondaryColor = Color(0xFF757575); // Grey 600
  // static const Color dividerColor = Color(0xFFBDBDBD); // Grey 400

  // Define color constants for ZABAI
  static const Color primaryColor = Color(0xFF0193A0);
  static const Color secondaryColor = Color(0xFF33a8b3);
  static const Color accentColor = Color(0xFF66bec6);
  static const Color backgroundColor = Colors.white;
  static const Color cardColor = Colors.white;
  static const Color errorColor = Color(0xFFB71C1C); // Red 900
  static const Color successColor = Color(0xFF43A047); // Green 600
  static const Color textPrimaryColor = Color(0xFF212121); // Grey 900
  static const Color textSecondaryColor = Color(0xFF757575); // Grey 600
  static const Color dividerColor = Color(0xFFBDBDBD); // Grey 400

  // Dark theme colors
  static const Color darkPrimaryColor = Color(0xFF0193A0);
  static const Color darkSecondaryColor = Color(0xFF33a8b3);
  static const Color darkAccentColor = Color(0xFF66bec6);
  static const Color darkBackgroundColor = Color(0xFF121212); // Dark background
  static const Color darkCardColor = Color(0xFF1E1E1E); // Dark card color
  static const Color darkTextPrimaryColor = Color(0xFFE0E0E0); // Light text for dark background
  static const Color darkTextSecondaryColor = Color(0xFFAAAAAA); // Secondary text for dark background
  static const Color darkDividerColor = Color(0xFF424242); // Dark divider

  // Font sizes
  static const double fontSizeSmall = 12.0;
  static const double fontSizeRegular = 14.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeXLarge = 20.0;
  static const double fontSizeXXLarge = 24.0;

  // Spacing
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;
  static const double spacingXXLarge = 48.0;

  // Border radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;
  static const double borderRadiusCircular = 100.0;

  // Card aspect ratios
  static const double cardAspectRatio = 0.8; // Standard aspect ratio for all cards (width/height)

  // Elevation
  static const double elevationSmall = 1.0;
  static const double elevationMedium = 2.0;
  static const double elevationLarge = 4.0;
  static const double elevationXLarge = 8.0;

  // Animation durations
  static const Duration animationDurationShort = Duration(milliseconds: 150);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);

  // Common text styles - Light theme
  static const TextStyle _displayLargeLight = TextStyle(
    fontSize: fontSizeXXLarge + 4,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
  );

  static const TextStyle _displayMediumLight = TextStyle(
    fontSize: fontSizeXXLarge,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
  );

  static const TextStyle _displaySmallLight = TextStyle(
    fontSize: fontSizeXLarge,
    fontWeight: FontWeight.bold,
    color: textPrimaryColor,
  );

  static const TextStyle _headlineLargeLight = TextStyle(
    fontSize: fontSizeXLarge,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
  );

  static const TextStyle _headlineMediumLight = TextStyle(
    fontSize: fontSizeLarge,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
  );

  static const TextStyle _headlineSmallLight = TextStyle(
    fontSize: fontSizeMedium,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
  );

  static const TextStyle _titleLargeLight = TextStyle(
    fontSize: fontSizeLarge,
    fontWeight: FontWeight.w600,
    color: textPrimaryColor,
  );

  static const TextStyle _titleMediumLight = TextStyle(
    fontSize: fontSizeMedium,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );

  static const TextStyle _titleSmallLight = TextStyle(
    fontSize: fontSizeRegular,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );

  static const TextStyle _bodyLargeLight = TextStyle(
    fontSize: fontSizeMedium,
    color: textPrimaryColor,
  );

  static const TextStyle _bodyMediumLight = TextStyle(
    fontSize: fontSizeRegular,
    color: textPrimaryColor,
  );

  static const TextStyle _bodySmallLight = TextStyle(
    fontSize: fontSizeSmall,
    color: textSecondaryColor,
  );

  static const TextStyle _labelLargeLight = TextStyle(
    fontSize: fontSizeRegular,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );

  static const TextStyle _labelMediumLight = TextStyle(
    fontSize: fontSizeSmall,
    fontWeight: FontWeight.w500,
    color: textPrimaryColor,
  );

  static const TextStyle _labelSmallLight = TextStyle(
    fontSize: fontSizeSmall - 2,
    fontWeight: FontWeight.w500,
    color: textSecondaryColor,
  );

  // Common text styles - Dark theme
  static const TextStyle _displayLargeDark = TextStyle(
    fontSize: fontSizeXXLarge + 4,
    fontWeight: FontWeight.bold,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _displayMediumDark = TextStyle(
    fontSize: fontSizeXXLarge,
    fontWeight: FontWeight.bold,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _displaySmallDark = TextStyle(
    fontSize: fontSizeXLarge,
    fontWeight: FontWeight.bold,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _headlineLargeDark = TextStyle(
    fontSize: fontSizeXLarge,
    fontWeight: FontWeight.w600,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _headlineMediumDark = TextStyle(
    fontSize: fontSizeLarge,
    fontWeight: FontWeight.w600,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _headlineSmallDark = TextStyle(
    fontSize: fontSizeMedium,
    fontWeight: FontWeight.w600,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _titleLargeDark = TextStyle(
    fontSize: fontSizeLarge,
    fontWeight: FontWeight.w600,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _titleMediumDark = TextStyle(
    fontSize: fontSizeMedium,
    fontWeight: FontWeight.w500,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _titleSmallDark = TextStyle(
    fontSize: fontSizeRegular,
    fontWeight: FontWeight.w500,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _bodyLargeDark = TextStyle(
    fontSize: fontSizeMedium,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _bodyMediumDark = TextStyle(
    fontSize: fontSizeRegular,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _bodySmallDark = TextStyle(
    fontSize: fontSizeSmall,
    color: darkTextSecondaryColor,
  );

  static const TextStyle _labelLargeDark = TextStyle(
    fontSize: fontSizeRegular,
    fontWeight: FontWeight.w500,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _labelMediumDark = TextStyle(
    fontSize: fontSizeSmall,
    fontWeight: FontWeight.w500,
    color: darkTextPrimaryColor,
  );

  static const TextStyle _labelSmallDark = TextStyle(
    fontSize: fontSizeSmall - 2,
    fontWeight: FontWeight.w500,
    color: darkTextSecondaryColor,
  );

  // Common shapes and paddings
  static const EdgeInsets _paddingMedium = EdgeInsets.all(spacingMedium);
  static const EdgeInsets _paddingHorizontalLargeVerticalMedium = EdgeInsets.symmetric(
    horizontal: spacingLarge,
    vertical: spacingMedium,
  );
  static const EdgeInsets _paddingHorizontalMediumVerticalSmall = EdgeInsets.symmetric(
    horizontal: spacingMedium,
    vertical: spacingSmall,
  );
  static const EdgeInsets _paddingSmall = EdgeInsets.all(spacingSmall);

  // Common sizes
  static const Size _buttonMinSize = Size(88, 48);

  /// Get the light theme for the app
  static ThemeData getLightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        tertiary: accentColor,
        // Use surface instead of background (deprecated)
        surface: cardColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimaryColor,
        onError: Colors.white,
      ),

      // General theme settings
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardColor,
      dividerColor: dividerColor,

      // Typography
      textTheme: const TextTheme(
        displayLarge: _displayLargeLight,
        displayMedium: _displayMediumLight,
        displaySmall: _displaySmallLight,
        headlineLarge: _headlineLargeLight,
        headlineMedium: _headlineMediumLight,
        headlineSmall: _headlineSmallLight,
        titleLarge: _titleLargeLight,
        titleMedium: _titleMediumLight,
        titleSmall: _titleSmallLight,
        bodyLarge: _bodyLargeLight,
        bodyMedium: _bodyMediumLight,
        bodySmall: _bodySmallLight,
        labelLarge: _labelLargeLight,
        labelMedium: _labelMediumLight,
        labelSmall: _labelSmallLight,
      ),

      // AppBar theme
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: elevationMedium,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: fontSizeLarge,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: elevationMedium,
          padding: _paddingHorizontalLargeVerticalMedium,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
          minimumSize: _buttonMinSize,
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor, width: 1.5),
          padding: _paddingHorizontalLargeVerticalMedium,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
          minimumSize: _buttonMinSize,
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: _paddingHorizontalMediumVerticalSmall,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        contentPadding: _paddingMedium,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: dividerColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
        labelStyle: const TextStyle(
          color: textSecondaryColor,
          fontSize: fontSizeRegular,
        ),
        hintStyle: TextStyle(
          // Use withAlpha instead of withOpacity (deprecated)
          color: textSecondaryColor.withAlpha(179), // ~0.7 opacity
          fontSize: fontSizeRegular,
        ),
        errorStyle: const TextStyle(
          color: errorColor,
          fontSize: fontSizeSmall,
        ),
        prefixIconColor: textSecondaryColor,
        suffixIconColor: textSecondaryColor,
      ),

      // Card theme
      cardTheme: CardTheme(
        color: cardColor,
        elevation: elevationMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
        margin: _paddingSmall,
      ),

      // List tile theme
      listTileTheme: ListTileThemeData(
        contentPadding: _paddingHorizontalMediumVerticalSmall,
        tileColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
      ),

      // Floating action button theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: elevationMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusCircular),
        ),
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: elevationLarge,
        selectedLabelStyle: TextStyle(
          fontSize: fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: fontSizeSmall,
          fontWeight: FontWeight.normal,
        ),
      ),

      // Divider theme
      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: spacingMedium,
      ),

      // Snackbar theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: textPrimaryColor,
        contentTextStyle: const TextStyle(
          color: Colors.white,
          fontSize: fontSizeRegular,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        behavior: SnackBarBehavior.floating,
        elevation: elevationMedium,
      ),
    );
  }

  /// Get the dark theme for the app
  static ThemeData getDarkTheme() {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: darkPrimaryColor,
        secondary: darkSecondaryColor,
        tertiary: darkAccentColor,
        // Use surface instead of background (deprecated)
        surface: darkCardColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: darkTextPrimaryColor,
        onError: Colors.white,
      ),

      // General theme settings
      scaffoldBackgroundColor: darkBackgroundColor,
      cardColor: darkCardColor,
      dividerColor: darkDividerColor,

      // Typography - same structure as light theme but with dark colors
      textTheme: const TextTheme(
        displayLarge: _displayLargeDark,
        displayMedium: _displayMediumDark,
        displaySmall: _displaySmallDark,
        headlineLarge: _headlineLargeDark,
        headlineMedium: _headlineMediumDark,
        headlineSmall: _headlineSmallDark,
        titleLarge: _titleLargeDark,
        titleMedium: _titleMediumDark,
        titleSmall: _titleSmallDark,
        bodyLarge: _bodyLargeDark,
        bodyMedium: _bodyMediumDark,
        bodySmall: _bodySmallDark,
        labelLarge: _labelLargeDark,
        labelMedium: _labelMediumDark,
        labelSmall: _labelSmallDark,
      ),

      // AppBar theme
      appBarTheme: const AppBarTheme(
        backgroundColor: darkPrimaryColor,
        foregroundColor: Colors.white,
        elevation: elevationMedium,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: fontSizeLarge,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),

      // Button themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: darkPrimaryColor,
          foregroundColor: Colors.white,
          elevation: elevationMedium,
          padding: _paddingHorizontalLargeVerticalMedium,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadiusMedium),
          ),
          textStyle: const TextStyle(
            fontSize: fontSizeMedium,
            fontWeight: FontWeight.bold,
          ),
          minimumSize: _buttonMinSize,
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkCardColor,
        contentPadding: _paddingMedium,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: darkDividerColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: darkDividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: darkPrimaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor, width: 1),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: errorColor, width: 2),
        ),
        labelStyle: const TextStyle(
          color: darkTextSecondaryColor,
          fontSize: fontSizeRegular,
        ),
        hintStyle: TextStyle(
          // Use withAlpha instead of withOpacity (deprecated)
          color: darkTextSecondaryColor.withAlpha(179), // ~0.7 opacity
          fontSize: fontSizeRegular,
        ),
        errorStyle: const TextStyle(
          color: errorColor,
          fontSize: fontSizeSmall,
        ),
      ),

      // Card theme
      cardTheme: CardTheme(
        color: darkCardColor,
        elevation: elevationMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusLarge),
        ),
        margin: _paddingSmall,
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: darkCardColor,
        selectedItemColor: darkPrimaryColor,
        unselectedItemColor: darkTextSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: elevationLarge,
      ),

      // Snackbar theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: darkCardColor,
        contentTextStyle: const TextStyle(
          color: darkTextPrimaryColor,
          fontSize: fontSizeRegular,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        behavior: SnackBarBehavior.floating,
        elevation: elevationMedium,
      ),
    );
  }
}
