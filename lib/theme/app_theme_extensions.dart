import 'package:flutter/material.dart';
import 'app_theme.dart';

/// Extension methods for ThemeData to provide consistent styling across the app
extension ThemeDataExtensions on ThemeData {
  // Card styling
  CardTheme get standardCard {
    return CardTheme(
      elevation: AppTheme.elevationMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
      ),
      margin: const EdgeInsets.all(AppTheme.spacingSmall),
    );
  }

  CardTheme get smallCard {
    return CardTheme(
      elevation: AppTheme.elevationSmall,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
    );
  }

  // Button styling
  ButtonStyle get primaryButton => ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: AppTheme.elevationMedium,
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMedium,
          vertical: AppTheme.spacingSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        textStyle: const TextStyle(
          fontSize: AppTheme.fontSizeMedium,
          fontWeight: FontWeight.bold,
        ),
      );

  ButtonStyle get secondaryButton => ElevatedButton.styleFrom(
        backgroundColor: colorScheme.secondary,
        foregroundColor: colorScheme.onSecondary,
        elevation: AppTheme.elevationMedium,
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMedium,
          vertical: AppTheme.spacingSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        textStyle: const TextStyle(
          fontSize: AppTheme.fontSizeMedium,
          fontWeight: FontWeight.bold,
        ),
      );

  ButtonStyle get dangerButton => ElevatedButton.styleFrom(
        backgroundColor: colorScheme.error,
        foregroundColor: colorScheme.onError,
        elevation: AppTheme.elevationMedium,
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMedium,
          vertical: AppTheme.spacingSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        textStyle: const TextStyle(
          fontSize: AppTheme.fontSizeMedium,
          fontWeight: FontWeight.bold,
        ),
      );

  ButtonStyle get outlineButton => OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        side: BorderSide(color: colorScheme.primary),
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacingMedium,
          vertical: AppTheme.spacingSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        textStyle: const TextStyle(
          fontSize: AppTheme.fontSizeMedium,
          fontWeight: FontWeight.bold,
        ),
      );

  ButtonStyle get smallButton => primaryButton.copyWith(
        padding: MaterialStateProperty.all(
          const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSmall,
            vertical: 0,
          ),
        ),
        textStyle: MaterialStateProperty.all(
          const TextStyle(
            fontSize: AppTheme.fontSizeSmall,
            fontWeight: FontWeight.bold,
          ),
        ),
        minimumSize: MaterialStateProperty.all(const Size(0, 28)),
      );

  ButtonStyle get smallOutlineButton => outlineButton.copyWith(
        padding: MaterialStateProperty.all(
          const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSmall,
            vertical: 0,
          ),
        ),
        textStyle: MaterialStateProperty.all(
          const TextStyle(
            fontSize: AppTheme.fontSizeSmall,
            fontWeight: FontWeight.bold,
          ),
        ),
        minimumSize: MaterialStateProperty.all(const Size(0, 28)),
      );

  ButtonStyle get smallDangerButton => dangerButton.copyWith(
        padding: MaterialStateProperty.all(
          const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingSmall,
            vertical: 0,
          ),
        ),
        textStyle: MaterialStateProperty.all(
          const TextStyle(
            fontSize: AppTheme.fontSizeSmall,
            fontWeight: FontWeight.bold,
          ),
        ),
        minimumSize: MaterialStateProperty.all(const Size(0, 28)),
      );

  // Text styles
  TextStyle get cardTitle => textTheme.titleMedium!.copyWith(
        fontWeight: FontWeight.bold,
      );

  TextStyle get cardSubtitle => textTheme.bodyMedium!;

  TextStyle get smallText => textTheme.bodySmall!;

  TextStyle get boldSmallText => textTheme.bodySmall!.copyWith(
        fontWeight: FontWeight.bold,
      );

  // Button text styles
  TextStyle get primaryButtonText => smallText.copyWith(
        color: colorScheme.onPrimary,
        fontWeight: FontWeight.bold,
      );

  TextStyle get secondaryButtonText => smallText.copyWith(
        color: colorScheme.onSecondary,
        fontWeight: FontWeight.bold,
      );

  TextStyle get dangerButtonText => smallText.copyWith(
        color: colorScheme.onError,
        fontWeight: FontWeight.bold,
      );

  // Content type colors
  Color getContentTypeColor(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'pdf':
        return colorScheme.error;
      case 'video':
      case 'mp4':
        return colorScheme.primary;
      case 'audio':
      case 'mp3':
        return colorScheme.secondary;
      case 'link':
        return AppTheme.successColor;
      default:
        return colorScheme.primary;
    }
  }

  // Standard decorations
  BoxDecoration get roundedDecoration => BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      );

  // Standard paddings
  EdgeInsets get standardPadding => const EdgeInsets.all(AppTheme.spacingMedium);
  EdgeInsets get smallPadding => const EdgeInsets.all(AppTheme.spacingSmall);
  EdgeInsets get contentPadding => const EdgeInsets.symmetric(
        horizontal: AppTheme.spacingMedium,
        vertical: AppTheme.spacingSmall,
      );
}
