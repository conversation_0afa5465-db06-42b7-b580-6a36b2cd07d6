class LibraryItem {
  final int id;
  final String author;
  final String createdAt;
  final String contentType;
  final String contentTitle;
  final String contentLink;
  final String? contentSummary;
  final String contentFile;
  final String contentThumbnail;
  bool isDownloaded;
  String? localFilePath;
  String? contentCategory;

  LibraryItem({
    required this.id,
    required this.author,
    required this.createdAt,
    required this.contentType,
    required this.contentTitle,
    required this.contentLink,
    this.contentSummary,
    required this.contentFile,
    required this.contentThumbnail,
    this.isDownloaded = false,
    this.localFilePath,
    this.contentCategory,
  });

  // Create a copy of the library item with updated fields
  LibraryItem copyWith({
    int? id,
    String? author,
    String? createdAt,
    String? contentType,
    String? contentTitle,
    String? contentLink,
    String? contentSummary,
    String? contentFile,
    String? contentThumbnail,
    bool? isDownloaded,
    String? localFilePath,
    String? contentCategory,
  }) {
    return LibraryItem(
      id: id ?? this.id,
      author: author ?? this.author,
      createdAt: createdAt ?? this.createdAt,
      contentType: contentType ?? this.contentType,
      contentTitle: contentTitle ?? this.contentTitle,
      contentLink: contentLink ?? this.contentLink,
      contentSummary: contentSummary ?? this.contentSummary,
      contentFile: contentFile ?? this.contentFile,
      contentThumbnail: contentThumbnail ?? this.contentThumbnail,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      localFilePath: localFilePath ?? this.localFilePath,
      contentCategory: contentCategory ?? this.contentCategory,
    );
  }

  // Convert LibraryItem object to a Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'author': author,
      'created_at': createdAt,
      'content_type': contentType,
      'content_title': contentTitle,
      'content_link': contentLink,
      'content_summary': contentSummary,
      'content_file': contentFile,
      'content_thumbnail': contentThumbnail,
      'is_downloaded': isDownloaded ? 1 : 0,
      'local_file_path': localFilePath,
      'category': contentCategory,
    };
  }

  // Create a LibraryItem object from a database Map
  factory LibraryItem.fromMap(Map<String, dynamic> map) {
    return LibraryItem(
      id: map['id'] ?? 0,
      author: map['author'] ?? '',
      createdAt: map['created_at'] ?? '',
      contentType: map['content_type'] ?? '',
      contentTitle: map['content_title'] ?? '',
      contentLink: map['content_link'] ?? '',
      contentSummary: map['content_summary'],
      contentFile: map['content_file'] ?? '',
      contentThumbnail: map['content_thumbnail'] ?? '',
      isDownloaded: map['is_downloaded'] == 1,
      localFilePath: map['local_file_path'],
      contentCategory: map['category'],
    );
  }

  // Create a LibraryItem object from API response
  factory LibraryItem.fromJson(Map<String, dynamic> json) {
    return LibraryItem(
      id: json['id'] ?? 0,
      author: json['author'] ?? '',
      createdAt: json['created_at'] ?? '',
      contentType: json['content_type'] ?? '',
      contentTitle: json['content_title'] ?? '',
      contentLink: json['content_link'] ?? '',
      contentSummary: json['content_summary'],
      contentFile: json['content_file'] ?? '',
      contentThumbnail: json['content_thumbnail'] ?? '',
      contentCategory: json['category'] ?? '',
      isDownloaded: false,
      localFilePath: null,
    );
  }

  // Get file extension from content file
  String get fileExtension {
    final parts = contentFile.split('.');
    return parts.length > 1 ? parts.last.toLowerCase() : '';
  }

  // Check if the content is a PDF
  bool get isPdf => fileExtension == 'pdf';

  // Check if the content is an audio file
  bool get isAudio => ['mp3', 'wav', 'aac', 'm4a'].contains(fileExtension);

  // Check if the content is a video file
  bool get isVideo => ['mp4', 'mov', 'avi', 'mkv', 'webm'].contains(fileExtension);

  // Check if the item is an external link (empty content_file and non-empty content_link)
  bool get isExternalLink => contentFile.isEmpty && contentLink.isNotEmpty;

  // Get the appropriate icon for the content type
  String get contentTypeIcon {
    if (isExternalLink) return 'assets/icons/link_icon.png';
    if (isPdf) return 'assets/icons/pdf_icon.png';
    if (isAudio) return 'assets/icons/audio_icon.png';
    if (isVideo) return 'assets/icons/video_icon.png';
    return 'assets/icons/file_icon.png';
  }

  @override
  String toString() {
    return 'LibraryItem{id: $id, title: $contentTitle, type: $contentType}';
  }
}
