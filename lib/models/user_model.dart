import 'dart:convert';
import '../services/logger_service.dart';

class User {
  final int id;
  final String username;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String avatar;
  final String coverPhoto;
  final String summary;
  final String token;
  final String createdAt;
  final String updatedAt;
  final Map<String, dynamic> experiences;

  User({
    required this.id,
    required this.username,
    required this.firstName,
    required this.lastName,
    this.email = '',
    this.phone = '',
    this.avatar = '',
    this.coverPhoto = '',
    this.summary = '',
    required this.token,
    required this.createdAt,
    required this.updatedAt,
    this.experiences = const {},
  });

  User copyWith({
    int? id,
    String? username,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? avatar,
    String? coverPhoto,
    String? summary,
    String? token,
    String? createdAt,
    String? updatedAt,
    Map<String, dynamic>? experiences,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      coverPhoto: coverPhoto ?? this.coverPhoto,
      summary: summary ?? this.summary,
      token: token ?? this.token,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      experiences: experiences ?? this.experiences,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'cover_photo': coverPhoto,
      'summary': summary,
      'token': token,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'experiences': jsonEncode(experiences),
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'] ?? 0,
      username: map['username'] ?? '',
      firstName: map['first_name'] ?? '',
      lastName: map['last_name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      avatar: map['avatar'] ?? '',
      coverPhoto: map['cover_photo'] ?? '',
      summary: map['summary'] ?? '',
      token: map['token'] ?? '',
      createdAt: map['created_at'] ?? DateTime.now().toIso8601String(),
      updatedAt: map['updated_at'] ?? DateTime.now().toIso8601String(),
      experiences: jsonDecode(map['experiences'] ?? '{}'),
    );
  }

  factory User.fromLoginResponse(Map<String, dynamic> response) {
    final payload = response['payload'] ?? {};
    final user = payload['user'] ?? {};

    return User(
      id: user['id'] ?? 0,
      username: user['username'] ?? '',
      firstName: '',
      lastName: '',
      email: user['email'] ?? '',
      phone: '',
      avatar: user['avatar'] ?? '',
      coverPhoto: '',
      summary: '',
      token: payload['token'] ?? '',
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
      experiences: const {},
    );
  }

  factory User.fromProfileResponse(Map<String, dynamic> response, User currentUser) {
    String email = currentUser.email;
    String phone = currentUser.phone;
    Map<String, dynamic> experiences = {};

    try {
      final detailsString = response['details'] as String?;
      if (detailsString != null && detailsString.isNotEmpty) {
        final details = jsonDecode(detailsString) as Map<String, dynamic>;

        final contactInfo = details['contact_information'] as Map<String, dynamic>?;
        if (contactInfo != null) {
          email = contactInfo['email'] as String? ?? email;
          phone = contactInfo['phone_number'] as String? ?? phone;
        }
        experiences = details['experiences'] as Map<String, dynamic>? ?? {};
      }
    } catch (e) {
      LoggerService.warning('Failed to parse user details JSON: $e');
    }

    return User(
      id: response['id'] ?? currentUser.id,
      username: currentUser.username,
      firstName: response['first_name'] ?? currentUser.firstName,
      lastName: response['last_name'] ?? currentUser.lastName,
      email: email,
      phone: phone,
      avatar: response['avatar'] ?? currentUser.avatar,
      coverPhoto: response['cover'] ?? currentUser.coverPhoto,
      summary: response['summary'] ?? currentUser.summary,
      token: currentUser.token,
      createdAt: currentUser.createdAt,
      updatedAt: DateTime.now().toIso8601String(),
      experiences: experiences,
    );
  }

  User mergeProfileData(Map<String, dynamic> profileData) {
    String email = this.email;
    String phone = this.phone;
    Map<String, dynamic> experiences = this.experiences;

    try {
      final detailsString = profileData['details'] as String?;
      if (detailsString != null && detailsString.isNotEmpty) {
        final details = jsonDecode(detailsString) as Map<String, dynamic>;

        final contactInfo = details['contact_information'] as Map<String, dynamic>?;
        if (contactInfo != null) {
          email = contactInfo['email'] as String? ?? email;
          phone = contactInfo['phone_number'] as String? ?? phone;
        }
        experiences = details['experiences'] as Map<String, dynamic>? ?? experiences;
      }
    } catch (e) {
      LoggerService.warning('Failed to parse user details JSON in merge: $e');
    }

    return copyWith(
      firstName: profileData['first_name'] ?? firstName,
      lastName: profileData['last_name'] ?? lastName,
      email: email,
      phone: phone,
      avatar: profileData['avatar'] ?? avatar,
      coverPhoto: profileData['cover'] ?? coverPhoto,
      summary: profileData['summary'] ?? summary,
      updatedAt: DateTime.now().toIso8601String(),
      experiences: experiences,
    );
  }

  String get fullName => '$firstName $lastName'.trim();

  @override
  String toString() {
    return 'User{id: $id, username: $username, name: $fullName}';
  }
}

