class Course {
  final int id;
  final String banner;
  final String title;
  final String slug;
  final String description;
  final String language;
  final String category;
  final List<String> tags;
  final List<Module> modules;
  final String? summary;
  final String? startDate;
  final String? endDate;

  Course({
    required this.id,
    required this.banner,
    required this.title,
    required this.slug,
    required this.description,
    this.language = '',
    this.category = '',
    this.tags = const [],
    required this.modules,
    this.summary,
    this.startDate,
    this.endDate,
  });

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'] ?? 0,
      // Handle both old and new API response formats
      banner: json['thumbnail'] ?? json['course_banner'] ?? '',
      title: json['name'] ?? json['course_title'] ?? 'Untitled Course',
      slug: json['slug'] ?? json['course_slug'] ?? '',
      description: json['description'] ?? 'No description available.',
      language: json['language'] ?? '',
      // Handle category as both string and array
      category: _extractCategory(json['category']),
      tags: (json['tags'] as List?)?.map((tag) => tag.toString()).toList() ?? [],
      modules: (json['modules'] as List?)
          ?.map((module) => Module.fromJson(module))
          .toList() ??
          [],
      summary: json['summary'],
      startDate: json['start_date'],
      endDate: json['end_date'],
    );
  }

  // Helper method to extract category from either string or array
  static String _extractCategory(dynamic category) {
    if (category == null) return '';
    if (category is String) return category;
    if (category is List && category.isNotEmpty) {
      return category.first.toString();
    }
    return '';
  }
}

/// Enrolled course model for courses the user is enrolled in
/// This represents the response from apiMyCourses endpoint
class EnrolledCourse {
  final int id;
  final double completionStatus;
  final Map<String, dynamic>? certificate;
  final List<EnrolledModule> modules;

  EnrolledCourse({
    required this.id,
    required this.completionStatus,
    this.certificate,
    required this.modules,
  });

  factory EnrolledCourse.fromJson(Map<String, dynamic> json) {
    return EnrolledCourse(
      id: json['id'] ?? 0,
      completionStatus: (json['completion_status'] ?? 0.0).toDouble(),
      certificate: json['certificate'] as Map<String, dynamic>?,
      modules: (json['modules'] as List?)
          ?.map((module) => EnrolledModule.fromJson(module))
          .toList() ??
          [],
    );
  }
}

/// Enrolled module model for modules within enrolled courses
class EnrolledModule {
  final int id;
  final bool completionStatus;
  final bool lock;
  final List<String> dependentModuleList;
  final ModuleContent? content;
  final Map<String, dynamic>? state;

  EnrolledModule({
    required this.id,
    required this.completionStatus,
    required this.lock,
    this.dependentModuleList = const [],
    this.content,
    this.state,
  });

  /// Convenience getter for accessible status (opposite of lock)
  bool get accessible => !lock;

  factory EnrolledModule.fromJson(Map<String, dynamic> json) {
    return EnrolledModule(
      id: json['id'] ?? 0,
      completionStatus: json['completion_status'] ?? false,
      lock: !(json['accessible'] ?? false), // Invert accessible to get lock status
      dependentModuleList: (json['dependent_module_list'] as List?)
          ?.map((item) => item.toString())
          .toList() ?? [],
      content: json['content'] != null
          ? ModuleContent.fromJson(json['content'] as Map<String, dynamic>)
          : null,
      state: json['state'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'completion_status': completionStatus,
      'accessible': accessible, // Use the getter which inverts lock
      'dependent_module_list': dependentModuleList,
      'content': content?.toJson(),
      'state': state,
    };
  }
}

/// Module content model for different content types
class ModuleContent {
  final String type;
  final String downloadLink;
  final String? scormDataPath;

  ModuleContent({
    required this.type,
    required this.downloadLink,
    this.scormDataPath,
  });

  factory ModuleContent.fromJson(Map<String, dynamic> json) {
    return ModuleContent(
      type: json['type'] ?? '',
      downloadLink: json['download_link'] ?? '',
      scormDataPath: json['scorm_data_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'download_link': downloadLink,
      'scorm_data_path': scormDataPath,
    };
  }

  /// Check if this is SCORM content
  bool get isScorm => type.toUpperCase() == 'SCORM';

  /// Check if this is PDF content
  bool get isPdf => type.toUpperCase() == 'PDF';

  /// Check if this is audio/video content
  bool get isAudioVideo => type.toUpperCase() == 'AUDIO / VIDEO';
}

class Module {
  final int id;
  final String courseName;
  final String description;
  final String name;
  final String moduleSlug;
  final String downloadLink;
  final String scormDataPath;
  final String? summary;
  final bool accessible;
  final List<int> dependentModuleList;
  final String? contentType;

  Module({
    required this.id,
    this.courseName = '',
    this.description = '',
    required this.name,
    required this.moduleSlug,
    required this.downloadLink,
    required this.scormDataPath,
    this.summary,
    this.accessible = true,
    this.dependentModuleList = const [],
    this.contentType,
  });

  factory Module.fromJson(Map<String, dynamic> json) {
    // Handle nested content structure in new API format
    final content = json['content'] as Map<String, dynamic>?;

    return Module(
      id: json['id'] ?? 0,
      courseName: json['course_name'] ?? '',
      description: json['description'] ?? '',
      name: json['name'] ?? 'Untitled Module',
      // Handle both old and new API response formats
      moduleSlug: json['slug'] ?? json['module_slug'] ?? '',
      // Extract from content object or fallback to direct fields
      downloadLink: content?['download_link'] ?? json['download_link'] ?? '',
      scormDataPath: content?['scorm_data_path'] ?? json['scorm_data_path'] ?? '',
      summary: json['summary'],
      accessible: json['accessible'] ?? true,
      dependentModuleList: (json['dependent_module_list'] as List?)
          ?.map((id) => id is int ? id : int.tryParse(id.toString()) ?? 0)
          .toList() ?? [],
      contentType: content?['type'],
    );
  }
}
