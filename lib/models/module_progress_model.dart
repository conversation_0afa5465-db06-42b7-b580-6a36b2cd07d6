import 'dart:convert';

/// Model for module progress data (for non-SCORM content like PDF, audio, video)
class ModuleProgress {
  final int? id;
  final int userId;
  final int moduleId;
  final String contentType;
  final bool completionStatus;
  final Map<String, dynamic>? progressData;
  final DateTime lastAccessed;
  final bool syncStatus;
  final int syncAttempts;
  final DateTime createdAt;
  final DateTime updatedAt;

  ModuleProgress({
    this.id,
    required this.userId,
    required this.moduleId,
    required this.contentType,
    this.completionStatus = false,
    this.progressData,
    required this.lastAccessed,
    this.syncStatus = false,
    this.syncAttempts = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'module_id': moduleId,
      'content_type': contentType,
      'completion_status': completionStatus ? 1 : 0,
      'progress_data': progressData != null ? jsonEncode(progressData) : null,
      'last_accessed': lastAccessed.toIso8601String(),
      'sync_status': syncStatus ? 1 : 0,
      'sync_attempts': syncAttempts,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create from database map
  factory ModuleProgress.fromMap(Map<String, dynamic> map) {
    return ModuleProgress(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      moduleId: map['module_id']?.toInt() ?? 0,
      contentType: map['content_type'] ?? '',
      completionStatus: (map['completion_status'] ?? 0) == 1,
      progressData: map['progress_data'] != null 
          ? jsonDecode(map['progress_data']) as Map<String, dynamic>?
          : null,
      lastAccessed: DateTime.parse(map['last_accessed'] ?? DateTime.now().toIso8601String()),
      syncStatus: (map['sync_status'] ?? 0) == 1,
      syncAttempts: map['sync_attempts']?.toInt() ?? 0,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// Convert to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'module_id': moduleId,
      'content_type': contentType,
      'completion_status': completionStatus,
      'progress_data': progressData,
      'last_accessed': lastAccessed.toIso8601String(),
      'sync_status': syncStatus,
      'sync_attempts': syncAttempts,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory ModuleProgress.fromJson(Map<String, dynamic> json) {
    return ModuleProgress(
      id: json['id']?.toInt(),
      userId: json['user_id']?.toInt() ?? 0,
      moduleId: json['module_id']?.toInt() ?? 0,
      contentType: json['content_type'] ?? '',
      completionStatus: json['completion_status'] ?? false,
      progressData: json['progress_data'] as Map<String, dynamic>?,
      lastAccessed: DateTime.parse(json['last_accessed'] ?? DateTime.now().toIso8601String()),
      syncStatus: json['sync_status'] ?? false,
      syncAttempts: json['sync_attempts']?.toInt() ?? 0,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// Create a copy with updated fields
  ModuleProgress copyWith({
    int? id,
    int? userId,
    int? moduleId,
    String? contentType,
    bool? completionStatus,
    Map<String, dynamic>? progressData,
    DateTime? lastAccessed,
    bool? syncStatus,
    int? syncAttempts,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ModuleProgress(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      moduleId: moduleId ?? this.moduleId,
      contentType: contentType ?? this.contentType,
      completionStatus: completionStatus ?? this.completionStatus,
      progressData: progressData ?? this.progressData,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      syncStatus: syncStatus ?? this.syncStatus,
      syncAttempts: syncAttempts ?? this.syncAttempts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Check if needs sync (not synced and has completion status)
  bool get needsSync {
    return !syncStatus && completionStatus;
  }

  /// Mark as synced
  ModuleProgress markAsSynced() {
    return copyWith(
      syncStatus: true,
      syncAttempts: 0,
      updatedAt: DateTime.now(),
    );
  }

  /// Increment sync attempts
  ModuleProgress incrementSyncAttempts() {
    return copyWith(
      syncAttempts: syncAttempts + 1,
      updatedAt: DateTime.now(),
    );
  }

  /// Create for PDF content
  factory ModuleProgress.forPdf({
    required int userId,
    required int moduleId,
    bool completed = false,
    Map<String, dynamic>? progressData,
    int? id,
    bool syncStatus = false,
    int syncAttempts = 0,
  }) {
    final now = DateTime.now();
    return ModuleProgress(
      id: id,
      userId: userId,
      moduleId: moduleId,
      contentType: 'PDF',
      completionStatus: completed,
      progressData: progressData,
      lastAccessed: now,
      syncStatus: syncStatus,
      syncAttempts: syncAttempts,
      createdAt: id == null ? now : now,
      updatedAt: now,
    );
  }

  /// Create for audio/video content
  factory ModuleProgress.forAudioVideo({
    required int userId,
    required int moduleId,
    bool completed = false,
    Map<String, dynamic>? progressData,
    int? id,
    bool syncStatus = false,
    int syncAttempts = 0,
  }) {
    final now = DateTime.now();
    return ModuleProgress(
      id: id,
      userId: userId,
      moduleId: moduleId,
      contentType: 'Audio / Video',
      completionStatus: completed,
      progressData: progressData,
      lastAccessed: now,
      syncStatus: syncStatus,
      syncAttempts: syncAttempts,
      createdAt: id == null ? now : now,
      updatedAt: now,
    );
  }

  @override
  String toString() {
    return 'ModuleProgress(id: $id, userId: $userId, moduleId: $moduleId, contentType: $contentType, completionStatus: $completionStatus, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ModuleProgress &&
        other.id == id &&
        other.userId == userId &&
        other.moduleId == moduleId &&
        other.contentType == contentType &&
        other.completionStatus == completionStatus;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        moduleId.hashCode ^
        contentType.hashCode ^
        completionStatus.hashCode;
  }
}
