import 'question_model.dart';

/// Model for career guidance question category
class CareerQuestionCategory {
  final String name;
  final List<CareerQuestion> questions;

  CareerQuestionCategory({
    required this.name,
    required this.questions,
  });

  factory CareerQuestionCategory.fromJson(Map<String, dynamic> json) {
    return CareerQuestionCategory(
      name: json['name'] ?? '',
      questions: (json['questions'] as List<dynamic>?)
              ?.map((q) => CareerQuestion.fromJson(q))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'questions': questions.map((q) => q.toJson()).toList(),
    };
  }
}

/// Model for individual career guidance question
class CareerQuestion {
  final int? id;
  final String statement;
  final String? image;
  LikertScale? answer;

  CareerQuestion({
    this.id,
    required this.statement,
    this.image,
    this.answer,
  });

  factory CareerQuestion.fromJson(Map<String, dynamic> json) {
    return CareerQuestion(
      id: json['id'],
      statement: json['statement'] ?? '',
      image: json['image'],
      answer: null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'statement': statement,
      'image': image,
    };
  }

  /// Convert to response format for API
  Map<String, dynamic> toResponseJson() {
    return {
      'id': id,
      'answer':
          answer?.value ?? '', // Send exact string format required by server
    };
  }
}

/// Model for user's quiz response
class CareerQuizResponse {
  final List<CareerQuestionResponse> responses;

  CareerQuizResponse({
    required this.responses,
  });

  factory CareerQuizResponse.fromJson(Map<String, dynamic> json) {
    return CareerQuizResponse(
      responses: (json['responses'] as List<dynamic>?)
              ?.map((r) => CareerQuestionResponse.fromJson(r))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'responses': responses.map((r) => r.toJson()).toList(),
    };
  }
}

/// Model for individual question response
class CareerQuestionResponse {
  final int id;
  final String answer;

  CareerQuestionResponse({
    required this.id,
    required this.answer,
  });

  factory CareerQuestionResponse.fromJson(Map<String, dynamic> json) {
    return CareerQuestionResponse(
      id: json['id'] ?? 0,
      answer: json['answer'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'answer': answer,
    };
  }
}

/// Model for RIASEC score
class RiasecScore {
  final String name;
  final double score;

  RiasecScore({
    required this.name,
    required this.score,
  });

  factory RiasecScore.fromJson(Map<String, dynamic> json) {
    return RiasecScore(
      name: json['name'] ?? '',
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'score': score,
    };
  }

  /// Get the RIASEC category enum
  RiasecCategory? get category => RiasecCategory.fromString(name);

  /// Get color for this category
  String get colorHex {
    switch (category) {
      case RiasecCategory.realistic:
        return '#FF6B6B'; // Red
      case RiasecCategory.investigative:
        return '#4ECDC4'; // Teal
      case RiasecCategory.artistic:
        return '#45B7D1'; // Blue
      case RiasecCategory.social:
        return '#96CEB4'; // Green
      case RiasecCategory.enterprising:
        return '#FFEAA7'; // Yellow
      case RiasecCategory.conventional:
        return '#DDA0DD'; // Purple
      default:
        return '#95A5A6'; // Gray
    }
  }
}

/// Model for highest scoring category with description
class HighestCategory {
  final String name;
  final String description;

  HighestCategory({
    required this.name,
    required this.description,
  });

  factory HighestCategory.fromJson(Map<String, dynamic> json) {
    return HighestCategory(
      name: json['name'] ?? '',
      description: json['description'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
    };
  }

  /// Get the RIASEC category enum
  RiasecCategory? get category => RiasecCategory.fromString(name);
}

/// Model for complete career guidance results
class CareerGuidanceResult {
  final List<RiasecScore> scores;
  final HighestCategory highestCategory;
  final DateTime? completedAt;

  CareerGuidanceResult({
    required this.scores,
    required this.highestCategory,
    this.completedAt,
  });

  factory CareerGuidanceResult.fromJson(Map<String, dynamic> json) {
    return CareerGuidanceResult(
      scores: (json['scores'] as List<dynamic>?)
              ?.map((s) => RiasecScore.fromJson(s))
              .toList() ??
          [],
      highestCategory: HighestCategory.fromJson(json['highest_category'] ?? {}),
      completedAt: json['completed_at'] != null
          ? DateTime.tryParse(json['completed_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'scores': scores.map((s) => s.toJson()).toList(),
      'highest_category': highestCategory.toJson(),
      'completed_at': completedAt?.toIso8601String(),
    };
  }

  /// Get sorted scores (highest first)
  List<RiasecScore> get sortedScores {
    final sorted = List<RiasecScore>.from(scores);
    sorted.sort((a, b) => b.score.compareTo(a.score));
    return sorted;
  }

  /// Get the top 3 categories
  List<RiasecScore> get topThreeCategories {
    final sorted = sortedScores;
    return sorted.take(3).toList();
  }

  /// Check if user has completed the quiz
  bool get isCompleted => scores.isNotEmpty;

  /// Get overall completion percentage (for progress tracking)
  double get completionPercentage {
    if (scores.isEmpty) return 0.0;
    return 100.0;
  }
}

/// Model for quiz progress tracking
class QuizProgress {
  final int currentQuestionIndex;
  final int totalQuestions;
  final String currentCategory;
  final Map<String, List<CareerQuestion>> answeredQuestions;

  QuizProgress({
    required this.currentQuestionIndex,
    required this.totalQuestions,
    required this.currentCategory,
    required this.answeredQuestions,
  });

  /// Calculate progress percentage
  double get progressPercentage {
    if (totalQuestions == 0) return 0.0;
    return (currentQuestionIndex / totalQuestions) * 100;
  }

  /// Check if quiz is completed
  bool get isCompleted => currentQuestionIndex >= totalQuestions;

  /// Get remaining questions count
  int get remainingQuestions => totalQuestions - currentQuestionIndex;
}
