import 'dart:convert';

/// Model for SCORM progress data
class ScormProgress {
  final int? id;
  final int userId;
  final int moduleId;
  final Map<String, dynamic> scormData;
  final DateTime lastAccessed;
  final bool syncStatus;
  final int syncAttempts;
  final DateTime createdAt;
  final DateTime updatedAt;

  ScormProgress({
    this.id,
    required this.userId,
    required this.moduleId,
    required this.scormData,
    required this.lastAccessed,
    this.syncStatus = false,
    this.syncAttempts = 0,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a copy with updated fields
  ScormProgress copyWith({
    int? id,
    int? userId,
    int? moduleId,
    Map<String, dynamic>? scormData,
    DateTime? lastAccessed,
    bool? syncStatus,
    int? syncAttempts,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ScormProgress(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      moduleId: moduleId ?? this.moduleId,
      scormData: scormData ?? this.scormData,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      syncStatus: syncStatus ?? this.syncStatus,
      syncAttempts: syncAttempts ?? this.syncAttempts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert to database map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'module_id': moduleId,
      'scorm_data': jsonEncode(scormData),
      'last_accessed': lastAccessed.toIso8601String(),
      'sync_status': syncStatus ? 1 : 0,
      'sync_attempts': syncAttempts,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Create from database map
  factory ScormProgress.fromMap(Map<String, dynamic> map) {
    return ScormProgress(
      id: map['id'],
      userId: map['user_id'] ?? 0,
      moduleId: map['module_id'] ?? 0,
      scormData: jsonDecode(map['scorm_data'] ?? '{}'),
      lastAccessed: DateTime.parse(map['last_accessed'] ?? DateTime.now().toIso8601String()),
      syncStatus: (map['sync_status'] ?? 0) == 1,
      syncAttempts: map['sync_attempts'] ?? 0,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(map['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// Create from SCORM CMI data
  factory ScormProgress.fromCmiData({
    required int userId,
    required int moduleId,
    required Map<String, dynamic> cmiData,
    int? id,
    bool syncStatus = false,
    int syncAttempts = 0,
  }) {
    final now = DateTime.now();
    return ScormProgress(
      id: id,
      userId: userId,
      moduleId: moduleId,
      scormData: cmiData,
      lastAccessed: now,
      syncStatus: syncStatus,
      syncAttempts: syncAttempts,
      createdAt: id == null ? now : now, // If no ID, it's a new record
      updatedAt: now,
    );
  }

  /// Get specific CMI value
  String getCmiValue(String element) {
    return scormData[element]?.toString() ?? '';
  }

  /// Check if module is completed
  bool get isCompleted {
    final lessonStatus = getCmiValue('cmi.core.lesson_status');
    return lessonStatus == 'completed' || lessonStatus == 'passed';
  }

  /// Get lesson status
  String get lessonStatus {
    return getCmiValue('cmi.core.lesson_status');
  }

  /// Get suspend data
  String get suspendData {
    return getCmiValue('cmi.suspend_data');
  }

  /// Get lesson location (bookmark)
  String get lessonLocation {
    return getCmiValue('cmi.core.lesson_location');
  }

  /// Get session time
  String get sessionTime {
    return getCmiValue('cmi.core.session_time');
  }

  /// Get total time
  String get totalTime {
    return getCmiValue('cmi.core.total_time');
  }

  /// Get score
  Map<String, String> get score {
    return {
      'raw': getCmiValue('cmi.core.score.raw'),
      'min': getCmiValue('cmi.core.score.min'),
      'max': getCmiValue('cmi.core.score.max'),
    };
  }

  /// Check if needs sync (not synced and has data)
  bool get needsSync {
    return !syncStatus && scormData.isNotEmpty;
  }

  /// Mark as synced
  ScormProgress markAsSynced() {
    return copyWith(
      syncStatus: true,
      syncAttempts: 0,
      updatedAt: DateTime.now(),
    );
  }

  /// Increment sync attempts
  ScormProgress incrementSyncAttempts() {
    return copyWith(
      syncAttempts: syncAttempts + 1,
      updatedAt: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ScormProgress{id: $id, userId: $userId, moduleId: $moduleId, '
           'lessonStatus: $lessonStatus, syncStatus: $syncStatus, '
           'lastAccessed: $lastAccessed}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScormProgress &&
        other.userId == userId &&
        other.moduleId == moduleId;
  }

  @override
  int get hashCode {
    return userId.hashCode ^ moduleId.hashCode;
  }
}
