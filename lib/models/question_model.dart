enum LikertScale {
  stronglyDisagree('Strongly Disagree'),
  disagree('Disagree'),
  neutral('Neutral'),
  agree('Agree'),
  stronglyAgree('Strongly Agree');

  const LikertScale(this.value);
  final String value;

  static LikertScale? fromString(String? value) {
    if (value == null) return null;
    for (LikertScale scale in LikertScale.values) {
      if (scale.value == value) return scale;
    }
    return null;
  }

  // Get emoji representation for UI
  String get emoji {
    switch (this) {
      case LikertScale.stronglyDisagree:
        return '😴';
      case LikertScale.disagree:
        return '😔';
      case LikertScale.neutral:
        return '😐';
      case LikertScale.agree:
        return '🙂';
      case LikertScale.stronglyAgree:
        return '😃';
    }
  }

  // Get score value for RIASEC calculation
  int get scoreValue {
    switch (this) {
      case LikertScale.stronglyDisagree:
        return 1;
      case LikertScale.disagree:
        return 2;
      case LikertScale.neutral:
        return 3;
      case LikertScale.agree:
        return 4;
      case LikertScale.stronglyAgree:
        return 5;
    }
  }
}

enum RiasecCategory {
  realistic('Realistic'),
  investigative('Investigative'),
  artistic('Artistic'),
  social('Social'),
  enterprising('Enterprising'),
  conventional('Conventional');

  const RiasecCategory(this.value);
  final String value;

  static RiasecCategory? fromString(String? value) {
    if (value == null) return null;
    for (RiasecCategory category in RiasecCategory.values) {
      if (category.value.toLowerCase() == value.toLowerCase()) return category;
    }
    return null;
  }
}

class Question {
  final int? id; // Nullable, auto-generated by the database
  final String categoryName;
  final String statement;
  final String? image;
  final LikertScale? answer;

  Question({
    this.id,
    required this.categoryName,
    required this.statement,
    this.image,
    this.answer,
  });

  // Factory method to create a Question object from a Map (e.g., from database query results)
  factory Question.fromMap(Map<String, dynamic> map) {
    return Question(
        id: map['id'],
        categoryName: map['category_name'],
        statement: map['statement'],
        image: map['image'],
        answer: LikertScale.fromString(map['answer']));
  }

  // Factory method to create a Question object from API response
  factory Question.fromApiResponse(Map<String, dynamic> map) {
    return Question(
        id: map['id'],
        categoryName: map['name'] ?? '',
        statement: map['statement'] ?? '',
        image: map['image'],
        answer: null);
  }

  // Method to convert a Question object to a Map (e.g., for database insertion)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category_name': categoryName,
      'statement': statement,
      'image': image,
      'answer': answer?.value,
    };
  }
}
