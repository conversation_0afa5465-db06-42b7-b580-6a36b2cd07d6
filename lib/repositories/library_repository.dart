import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/library_item_model.dart';
import '../services/logger_service.dart';

class LibraryRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Insert or update a library item in the database
  Future<int> saveLibraryItem(LibraryItem item) async {
    try {
      final db = await _dbHelper.database;
      
      // Insert or replace library item
      return await db.insert(
        DatabaseHelper.tableLibraryItems,
        item.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      LoggerService.error('Error saving library item', e);
      return -1;
    }
  }

  // Save multiple library items
  Future<bool> saveLibraryItems(List<LibraryItem> items) async {
    try {
      final db = await _dbHelper.database;
      
      // Start a transaction
      await db.transaction((txn) async {
        for (var item in items) {
          await txn.insert(
            DatabaseHelper.tableLibraryItems,
            item.toMap(),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
      
      return true;
    } catch (e) {
      LoggerService.error('Error saving library items', e);
      return false;
    }
  }

  // Get all library items from the database
  Future<List<LibraryItem>> getLibraryItems() async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableLibraryItems,
        orderBy: '${DatabaseHelper.columnContentTitle} ASC',
      );
      
      return List.generate(maps.length, (i) {
        return LibraryItem.fromMap(maps[i]);
      });
    } catch (e) {
      LoggerService.error('Error getting library items', e);
      return [];
    }
  }

  // Get a library item by ID
  Future<LibraryItem?> getLibraryItemById(int id) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableLibraryItems,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [id],
      );
      
      if (maps.isEmpty) {
        return null;
      }
      
      return LibraryItem.fromMap(maps.first);
    } catch (e) {
      LoggerService.error('Error getting library item by ID', e);
      return null;
    }
  }

  // Update a library item's download status and local file path
  Future<int> updateDownloadStatus(int id, bool isDownloaded, String? localFilePath) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.update(
        DatabaseHelper.tableLibraryItems,
        {
          DatabaseHelper.columnIsDownloaded: isDownloaded ? 1 : 0,
          DatabaseHelper.columnLocalFilePath: localFilePath,
        },
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [id],
      );
    } catch (e) {
      LoggerService.error('Error updating download status', e);
      return -1;
    }
  }

  // Get all downloaded library items
  Future<List<LibraryItem>> getDownloadedItems() async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableLibraryItems,
        where: '${DatabaseHelper.columnIsDownloaded} = ?',
        whereArgs: [1],
        orderBy: '${DatabaseHelper.columnContentTitle} ASC',
      );
      
      return List.generate(maps.length, (i) {
        return LibraryItem.fromMap(maps[i]);
      });
    } catch (e) {
      LoggerService.error('Error getting downloaded items', e);
      return [];
    }
  }

  // Filter library items by content type
  Future<List<LibraryItem>> filterByContentType(String contentType) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableLibraryItems,
        where: '${DatabaseHelper.columnContentType} LIKE ?',
        whereArgs: ['%$contentType%'],
        orderBy: '${DatabaseHelper.columnContentTitle} ASC',
      );
      
      return List.generate(maps.length, (i) {
        return LibraryItem.fromMap(maps[i]);
      });
    } catch (e) {
      LoggerService.error('Error filtering library items by content type', e);
      return [];
    }
  }

  // Search library items by title
  Future<List<LibraryItem>> searchLibraryItems(String query) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableLibraryItems,
        where: '${DatabaseHelper.columnContentTitle} LIKE ?',
        whereArgs: ['%$query%'],
        orderBy: '${DatabaseHelper.columnContentTitle} ASC',
      );
      
      return List.generate(maps.length, (i) {
        return LibraryItem.fromMap(maps[i]);
      });
    } catch (e) {
      LoggerService.error('Error searching library items', e);
      return [];
    }
  }

  // Delete a library item
  Future<int> deleteLibraryItem(int id) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(
        DatabaseHelper.tableLibraryItems,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [id],
      );
    } catch (e) {
      LoggerService.error('Error deleting library item', e);
      return -1;
    }
  }

  // Clear all library items
  Future<int> clearLibraryItems() async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(DatabaseHelper.tableLibraryItems);
    } catch (e) {
      LoggerService.error('Error clearing library items', e);
      return -1;
    }
  }
}
