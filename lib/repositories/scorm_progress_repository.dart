import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/scorm_progress_model.dart';
import '../services/logger_service.dart';

/// Repository for managing SCORM progress data
class ScormProgressRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// Save or update SCORM progress
  Future<int> saveProgress(ScormProgress progress) async {
    try {
      final db = await _dbHelper.database;
      
      if (progress.id == null) {
        // Insert new progress
        return await db.insert(
          DatabaseHelper.tableScormProgress,
          progress.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        // Update existing progress
        return await db.update(
          DatabaseHelper.tableScormProgress,
          progress.toMap(),
          where: '${DatabaseHelper.columnId} = ?',
          whereArgs: [progress.id],
        );
      }
    } catch (e) {
      LoggerService.error('Error saving SCORM progress', e);
      return -1;
    }
  }

  /// Get SCORM progress for a specific user and module
  Future<ScormProgress?> getProgress(int userId, int moduleId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableScormProgress,
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [userId, moduleId],
      );
      
      if (maps.isEmpty) {
        return null;
      }
      
      return ScormProgress.fromMap(maps.first);
    } catch (e) {
      LoggerService.error('Error getting SCORM progress', e);
      return null;
    }
  }

  /// Get all SCORM progress for a user
  Future<List<ScormProgress>> getUserProgress(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableScormProgress,
        where: '${DatabaseHelper.columnUserId} = ?',
        whereArgs: [userId],
        orderBy: '${DatabaseHelper.columnLastAccessed} DESC',
      );
      
      return maps.map((map) => ScormProgress.fromMap(map)).toList();
    } catch (e) {
      LoggerService.error('Error getting user SCORM progress', e);
      return [];
    }
  }

  /// Get all unsynced SCORM progress
  Future<List<ScormProgress>> getUnsyncedProgress() async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableScormProgress,
        where: '${DatabaseHelper.columnSyncStatus} = ?',
        whereArgs: [0],
        orderBy: '${DatabaseHelper.columnUpdatedAt} ASC',
      );
      
      return maps.map((map) => ScormProgress.fromMap(map)).toList();
    } catch (e) {
      LoggerService.error('Error getting unsynced SCORM progress', e);
      return [];
    }
  }

  /// Update sync status for progress
  Future<int> updateSyncStatus(int progressId, bool synced, {int? syncAttempts}) async {
    try {
      final db = await _dbHelper.database;
      
      final Map<String, dynamic> updateData = {
        DatabaseHelper.columnSyncStatus: synced ? 1 : 0,
        DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
      };
      
      if (syncAttempts != null) {
        updateData[DatabaseHelper.columnSyncAttempts] = syncAttempts;
      }
      
      return await db.update(
        DatabaseHelper.tableScormProgress,
        updateData,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [progressId],
      );
    } catch (e) {
      LoggerService.error('Error updating sync status', e);
      return -1;
    }
  }

  /// Delete SCORM progress for a specific user and module
  Future<int> deleteProgress(int userId, int moduleId) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(
        DatabaseHelper.tableScormProgress,
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [userId, moduleId],
      );
    } catch (e) {
      LoggerService.error('Error deleting SCORM progress', e);
      return -1;
    }
  }

  /// Delete all SCORM progress for a user
  Future<int> deleteUserProgress(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(
        DatabaseHelper.tableScormProgress,
        where: '${DatabaseHelper.columnUserId} = ?',
        whereArgs: [userId],
      );
    } catch (e) {
      LoggerService.error('Error deleting user SCORM progress', e);
      return -1;
    }
  }

  /// Get progress count for a user
  Future<int> getProgressCount(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseHelper.tableScormProgress} WHERE ${DatabaseHelper.columnUserId} = ?',
        [userId],
      );
      
      return result.first['count'] as int? ?? 0;
    } catch (e) {
      LoggerService.error('Error getting progress count', e);
      return 0;
    }
  }

  /// Get completed modules count for a user
  Future<int> getCompletedModulesCount(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.rawQuery(
        '''SELECT COUNT(*) as count FROM ${DatabaseHelper.tableScormProgress} 
           WHERE ${DatabaseHelper.columnUserId} = ? 
           AND json_extract(${DatabaseHelper.columnScormData}, '\$."cmi.core.lesson_status"') IN ('completed', 'passed')''',
        [userId],
      );
      
      return result.first['count'] as int? ?? 0;
    } catch (e) {
      LoggerService.error('Error getting completed modules count', e);
      return 0;
    }
  }

  /// Update last accessed time
  Future<int> updateLastAccessed(int userId, int moduleId) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.update(
        DatabaseHelper.tableScormProgress,
        {
          DatabaseHelper.columnLastAccessed: DateTime.now().toIso8601String(),
          DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
        },
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [userId, moduleId],
      );
    } catch (e) {
      LoggerService.error('Error updating last accessed time', e);
      return -1;
    }
  }

  /// Batch update multiple progress records
  Future<void> batchUpdateProgress(List<ScormProgress> progressList) async {
    try {
      final db = await _dbHelper.database;
      
      await db.transaction((txn) async {
        for (final progress in progressList) {
          await txn.insert(
            DatabaseHelper.tableScormProgress,
            progress.toMap(),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
      
      LoggerService.debug('Batch updated ${progressList.length} SCORM progress records');
    } catch (e) {
      LoggerService.error('Error batch updating SCORM progress', e);
      rethrow;
    }
  }
}
