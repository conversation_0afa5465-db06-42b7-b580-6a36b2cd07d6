import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/course_model.dart';
import '../services/logger_service.dart';

class CourseRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Insert a course into the database
  Future<int> insertCourse(Course course) async {
    try {
      final db = await _dbHelper.database;
      
      // Begin transaction
      await db.transaction((txn) async {
        // Insert course
        await txn.insert(
          DatabaseHelper.tableCourses,
          {
            DatabaseHelper.columnId: course.id,
            DatabaseHelper.columnBanner: course.banner,
            DatabaseHelper.columnTitle: course.title,
            DatabaseHelper.columnSlug: course.slug,
            DatabaseHelper.columnDescription: course.description,
            DatabaseHelper.columnLanguage: course.language,
            DatabaseHelper.columnCategory: course.category,
            DatabaseHelper.columnCreatedAt: DateTime.now().toIso8601String(),
            DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
            DatabaseHelper.columnCourseSummary: course.summary,
            DatabaseHelper.columnStartDate: course.startDate,
            DatabaseHelper.columnEndDate: course.endDate,
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        
        // Insert modules
        for (var module in course.modules) {
          await txn.insert(
            DatabaseHelper.tableModules,
            {
              DatabaseHelper.columnId: module.id,
              DatabaseHelper.columnCourseId: course.id,
              DatabaseHelper.columnCourseName: module.courseName,
              DatabaseHelper.columnDescription: module.description,
              DatabaseHelper.columnName: module.name,
              DatabaseHelper.columnModuleSlug: module.moduleSlug,
              DatabaseHelper.columnDownloadLink: module.downloadLink,
              DatabaseHelper.columnScormDataPath: module.scormDataPath,
              DatabaseHelper.columnIsDownloaded: 0,
              DatabaseHelper.columnLocalPath: '',
              DatabaseHelper.columnModuleSummary: module.summary,
              DatabaseHelper.columnAccessible: module.accessible ? 1 : 0,
              DatabaseHelper.columnDependentModules: module.dependentModuleList.join(','),
              DatabaseHelper.columnModuleContentType: module.contentType,
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
        
        // Insert tags
        for (var tag in course.tags) {
          // First, try to insert the tag (if it doesn't exist)
          int tagId = await txn.insert(
            DatabaseHelper.tableTags,
            {DatabaseHelper.columnTagName: tag},
            conflictAlgorithm: ConflictAlgorithm.ignore,
          );
          
          // If the tag already exists, get its ID
          if (tagId == 0) {
            final List<Map<String, dynamic>> result = await txn.query(
              DatabaseHelper.tableTags,
              columns: [DatabaseHelper.columnId],
              where: '${DatabaseHelper.columnTagName} = ?',
              whereArgs: [tag],
            );
            
            if (result.isNotEmpty) {
              tagId = result.first[DatabaseHelper.columnId];
            }
          }
          
          // Insert the course-tag relationship
          if (tagId != 0) {
            await txn.insert(
              DatabaseHelper.tableCoursesTags,
              {
                DatabaseHelper.columnCourseId: course.id,
                DatabaseHelper.columnId: tagId,
              },
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
        }
      });
      
      return course.id;
    } catch (e) {
      LoggerService.error('Error inserting course', e);
      return -1;
    }
  }

  // Insert multiple courses
  Future<bool> insertCourses(List<Course> courses) async {
    try {
      for (var course in courses) {
        await insertCourse(course);
      }
      return true;
    } catch (e) {
      LoggerService.error('Error inserting courses', e);
      return false;
    }
  }

  // Get all courses
  Future<List<Course>> getAllCourses() async {
    try {
      final db = await _dbHelper.database;
      
      // Get all courses
      final List<Map<String, dynamic>> courseMaps = await db.query(DatabaseHelper.tableCourses);
      
      // Convert to Course objects
      return await Future.wait(courseMaps.map((courseMap) async {
        // Get modules for this course
        final List<Map<String, dynamic>> moduleMaps = await db.query(
          DatabaseHelper.tableModules,
          where: '${DatabaseHelper.columnCourseId} = ?',
          whereArgs: [courseMap[DatabaseHelper.columnId]],
        );
        
        // Get tags for this course
        final List<Map<String, dynamic>> tagMaps = await db.rawQuery('''
          SELECT t.${DatabaseHelper.columnTagName}
          FROM ${DatabaseHelper.tableTags} t
          INNER JOIN ${DatabaseHelper.tableCoursesTags} ct
            ON t.${DatabaseHelper.columnId} = ct.${DatabaseHelper.columnId}
          WHERE ct.${DatabaseHelper.columnCourseId} = ?
        ''', [courseMap[DatabaseHelper.columnId]]);
        
        // Convert modules to Module objects
        final List<Module> modules = moduleMaps.map((moduleMap) => Module(
          id: moduleMap[DatabaseHelper.columnId],
          courseName: moduleMap[DatabaseHelper.columnCourseName] ?? '',
          description: moduleMap[DatabaseHelper.columnDescription] ?? '',
          name: moduleMap[DatabaseHelper.columnName],
          moduleSlug: moduleMap[DatabaseHelper.columnModuleSlug],
          downloadLink: moduleMap[DatabaseHelper.columnDownloadLink] ?? '',
          scormDataPath: moduleMap[DatabaseHelper.columnScormDataPath] ?? '',
          summary: moduleMap[DatabaseHelper.columnModuleSummary],
          accessible: (moduleMap[DatabaseHelper.columnAccessible] ?? 1) == 1,
          dependentModuleList: _parseDependentModules(moduleMap[DatabaseHelper.columnDependentModules]),
          contentType: moduleMap[DatabaseHelper.columnModuleContentType],
        )).toList();
        
        // Extract tag names
        final List<String> tags = tagMaps.map((tagMap) => 
          tagMap[DatabaseHelper.columnTagName] as String
        ).toList();
        
        // Create and return the Course object
        return Course(
          id: courseMap[DatabaseHelper.columnId],
          banner: courseMap[DatabaseHelper.columnBanner],
          title: courseMap[DatabaseHelper.columnTitle],
          slug: courseMap[DatabaseHelper.columnSlug],
          description: courseMap[DatabaseHelper.columnDescription] ?? '',
          language: courseMap[DatabaseHelper.columnLanguage] ?? '',
          category: courseMap[DatabaseHelper.columnCategory] ?? '',
          tags: tags,
          modules: modules,
          summary: courseMap[DatabaseHelper.columnCourseSummary],
          startDate: courseMap[DatabaseHelper.columnStartDate],
          endDate: courseMap[DatabaseHelper.columnEndDate],
        );
      }).toList());
    } catch (e) {
      LoggerService.error('Error getting all courses', e);
      return [];
    }
  }

  // Get a course by ID
  Future<Course?> getCourseById(int id) async {
    try {
      final db = await _dbHelper.database;
      
      // Get the course
      final List<Map<String, dynamic>> courseMaps = await db.query(
        DatabaseHelper.tableCourses,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [id],
      );
      
      if (courseMaps.isEmpty) {
        return null;
      }
      
      final courseMap = courseMaps.first;
      
      // Get modules for this course
      final List<Map<String, dynamic>> moduleMaps = await db.query(
        DatabaseHelper.tableModules,
        where: '${DatabaseHelper.columnCourseId} = ?',
        whereArgs: [id],
      );
      
      // Get tags for this course
      final List<Map<String, dynamic>> tagMaps = await db.rawQuery('''
        SELECT t.${DatabaseHelper.columnTagName}
        FROM ${DatabaseHelper.tableTags} t
        INNER JOIN ${DatabaseHelper.tableCoursesTags} ct
          ON t.${DatabaseHelper.columnId} = ct.${DatabaseHelper.columnId}
        WHERE ct.${DatabaseHelper.columnCourseId} = ?
      ''', [id]);
      
      // Convert modules to Module objects
      final List<Module> modules = moduleMaps.map((moduleMap) => Module(
        id: moduleMap[DatabaseHelper.columnId],
        courseName: moduleMap[DatabaseHelper.columnCourseName] ?? '',
        description: moduleMap[DatabaseHelper.columnDescription] ?? '',
        name: moduleMap[DatabaseHelper.columnName],
        moduleSlug: moduleMap[DatabaseHelper.columnModuleSlug],
        downloadLink: moduleMap[DatabaseHelper.columnDownloadLink] ?? '',
        scormDataPath: moduleMap[DatabaseHelper.columnScormDataPath] ?? '',
        summary: moduleMap[DatabaseHelper.columnModuleSummary],
        accessible: (moduleMap[DatabaseHelper.columnAccessible] ?? 1) == 1,
        dependentModuleList: _parseDependentModules(moduleMap[DatabaseHelper.columnDependentModules]),
        contentType: moduleMap[DatabaseHelper.columnModuleContentType],
      )).toList();
      
      // Extract tag names
      final List<String> tags = tagMaps.map((tagMap) => 
        tagMap[DatabaseHelper.columnTagName] as String
      ).toList();
      
      // Create and return the Course object
      return Course(
        id: courseMap[DatabaseHelper.columnId],
        banner: courseMap[DatabaseHelper.columnBanner],
        title: courseMap[DatabaseHelper.columnTitle],
        slug: courseMap[DatabaseHelper.columnSlug],
        description: courseMap[DatabaseHelper.columnDescription] ?? '',
        language: courseMap[DatabaseHelper.columnLanguage] ?? '',
        category: courseMap[DatabaseHelper.columnCategory] ?? '',
        tags: tags,
        modules: modules,
        summary: courseMap[DatabaseHelper.columnCourseSummary],
        startDate: courseMap[DatabaseHelper.columnStartDate],
        endDate: courseMap[DatabaseHelper.columnEndDate],
      );
    } catch (e) {
      LoggerService.error('Error getting course by ID', e);
      return null;
    }
  }

  // Delete all courses
  Future<int> deleteAllCourses() async {
    try {
      final db = await _dbHelper.database;
      return await db.delete(DatabaseHelper.tableCourses);
    } catch (e) {
      LoggerService.error('Error deleting all courses', e);
      return 0;
    }
  }

  // Update module download status by module slug
  Future<bool> updateModuleDownloadStatus(String moduleSlug, bool isDownloaded, {String? localPath}) async {
    try {
      final db = await _dbHelper.database;

      final result = await db.update(
        DatabaseHelper.tableModules,
        {
          DatabaseHelper.columnIsDownloaded: isDownloaded ? 1 : 0,
          DatabaseHelper.columnLocalPath: localPath ?? '',
        },
        where: '${DatabaseHelper.columnModuleSlug} = ?',
        whereArgs: [moduleSlug],
      );

      LoggerService.debug('Updated module $moduleSlug download status to $isDownloaded');
      return result > 0;
    } catch (e) {
      LoggerService.error('Error updating module download status', e);
      return false;
    }
  }

  // Helper method to parse dependent modules from comma-separated string
  List<int> _parseDependentModules(String? dependentModulesStr) {
    if (dependentModulesStr == null || dependentModulesStr.isEmpty) {
      return [];
    }
    return dependentModulesStr
        .split(',')
        .where((str) => str.isNotEmpty)
        .map((str) => int.tryParse(str.trim()) ?? 0)
        .where((id) => id > 0)
        .toList();
  }
}
