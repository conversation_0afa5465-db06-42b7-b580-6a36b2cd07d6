import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/module_progress_model.dart';
import '../services/logger_service.dart';

/// Repository for managing module progress data (for non-SCORM content)
class ModuleProgressRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// Save or update module progress
  Future<int> saveProgress(ModuleProgress progress) async {
    try {
      final db = await _dbHelper.database;
      
      if (progress.id == null) {
        // Insert new progress
        return await db.insert(
          DatabaseHelper.tableModuleProgress,
          progress.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      } else {
        // Update existing progress
        return await db.update(
          DatabaseHelper.tableModuleProgress,
          progress.toMap(),
          where: '${DatabaseHelper.columnId} = ?',
          whereArgs: [progress.id],
        );
      }
    } catch (e) {
      LoggerService.error('Error saving module progress', e);
      return -1;
    }
  }

  /// Get module progress for a specific user and module
  Future<ModuleProgress?> getProgress(int userId, int moduleId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableModuleProgress,
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [userId, moduleId],
      );
      
      if (maps.isEmpty) {
        return null;
      }
      
      return ModuleProgress.fromMap(maps.first);
    } catch (e) {
      LoggerService.error('Error getting module progress', e);
      return null;
    }
  }

  /// Get all module progress for a user
  Future<List<ModuleProgress>> getUserProgress(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableModuleProgress,
        where: '${DatabaseHelper.columnUserId} = ?',
        whereArgs: [userId],
        orderBy: '${DatabaseHelper.columnLastAccessed} DESC',
      );
      
      return maps.map((map) => ModuleProgress.fromMap(map)).toList();
    } catch (e) {
      LoggerService.error('Error getting user module progress', e);
      return [];
    }
  }

  /// Get unsynced module progress
  Future<List<ModuleProgress>> getUnsyncedProgress() async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableModuleProgress,
        where: '${DatabaseHelper.columnSyncStatus} = ?',
        whereArgs: [0],
        orderBy: '${DatabaseHelper.columnUpdatedAt} ASC',
      );
      
      return maps.map((map) => ModuleProgress.fromMap(map)).toList();
    } catch (e) {
      LoggerService.error('Error getting unsynced module progress', e);
      return [];
    }
  }

  /// Update sync status for progress
  Future<int> updateSyncStatus(int progressId, bool synced, {int? syncAttempts}) async {
    try {
      final db = await _dbHelper.database;
      
      final Map<String, dynamic> updateData = {
        DatabaseHelper.columnSyncStatus: synced ? 1 : 0,
        DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
      };
      
      if (syncAttempts != null) {
        updateData[DatabaseHelper.columnSyncAttempts] = syncAttempts;
      }
      
      return await db.update(
        DatabaseHelper.tableModuleProgress,
        updateData,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [progressId],
      );
    } catch (e) {
      LoggerService.error('Error updating sync status', e);
      return -1;
    }
  }

  /// Delete module progress for a specific user and module
  Future<int> deleteProgress(int userId, int moduleId) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(
        DatabaseHelper.tableModuleProgress,
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [userId, moduleId],
      );
    } catch (e) {
      LoggerService.error('Error deleting module progress', e);
      return -1;
    }
  }

  /// Delete all progress for a user
  Future<int> deleteUserProgress(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(
        DatabaseHelper.tableModuleProgress,
        where: '${DatabaseHelper.columnUserId} = ?',
        whereArgs: [userId],
      );
    } catch (e) {
      LoggerService.error('Error deleting user module progress', e);
      return -1;
    }
  }

  /// Get progress by content type
  Future<List<ModuleProgress>> getProgressByContentType(int userId, String contentType) async {
    try {
      final db = await _dbHelper.database;

      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableModuleProgress,
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnProgressContentType} = ?',
        whereArgs: [userId, contentType],
        orderBy: '${DatabaseHelper.columnLastAccessed} DESC',
      );

      return maps.map((map) => ModuleProgress.fromMap(map)).toList();
    } catch (e) {
      LoggerService.error('Error getting progress by content type', e);
      return [];
    }
  }

  /// Get completed modules count for a user
  Future<int> getCompletedModulesCount(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.rawQuery(
        '''SELECT COUNT(*) as count FROM ${DatabaseHelper.tableModuleProgress}
           WHERE ${DatabaseHelper.columnUserId} = ?
           AND ${DatabaseHelper.columnProgressCompletionStatus} = 1''',
        [userId],
      );
      
      return result.first['count'] as int? ?? 0;
    } catch (e) {
      LoggerService.error('Error getting completed modules count', e);
      return 0;
    }
  }

  /// Update last accessed time
  Future<int> updateLastAccessed(int userId, int moduleId) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.update(
        DatabaseHelper.tableModuleProgress,
        {
          DatabaseHelper.columnLastAccessed: DateTime.now().toIso8601String(),
          DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
        },
        where: '${DatabaseHelper.columnUserId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [userId, moduleId],
      );
    } catch (e) {
      LoggerService.error('Error updating last accessed time', e);
      return -1;
    }
  }

  /// Batch update multiple progress records
  Future<void> batchUpdateProgress(List<ModuleProgress> progressList) async {
    try {
      final db = await _dbHelper.database;
      
      await db.transaction((txn) async {
        for (final progress in progressList) {
          await txn.insert(
            DatabaseHelper.tableModuleProgress,
            progress.toMap(),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        }
      });
      
      LoggerService.debug('Batch updated ${progressList.length} module progress records');
    } catch (e) {
      LoggerService.error('Error batch updating module progress', e);
      rethrow;
    }
  }

  /// Mark module as completed
  Future<int> markAsCompleted(int userId, int moduleId, String contentType, {Map<String, dynamic>? progressData}) async {
    try {
      final existingProgress = await getProgress(userId, moduleId);
      
      ModuleProgress progress;
      if (existingProgress != null) {
        progress = existingProgress.copyWith(
          completionStatus: true,
          progressData: progressData ?? existingProgress.progressData,
          lastAccessed: DateTime.now(),
          syncStatus: false, // Mark as needing sync
          updatedAt: DateTime.now(),
        );
      } else {
        progress = ModuleProgress(
          userId: userId,
          moduleId: moduleId,
          contentType: contentType,
          completionStatus: true,
          progressData: progressData,
          lastAccessed: DateTime.now(),
          syncStatus: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }
      
      return await saveProgress(progress);
    } catch (e) {
      LoggerService.error('Error marking module as completed', e);
      return -1;
    }
  }
}
