import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/course_model.dart';
import '../models/scorm_progress_model.dart';
import '../services/logger_service.dart';

class EnrolledCourseRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// Insert or update an enrolled course with its modules
  Future<int> insertEnrolledCourse(EnrolledCourse enrolledCourse, int userId) async {
    try {
      final db = await _dbHelper.database;

      // Begin transaction
      await db.transaction((txn) async {
        // Insert or update enrolled course
        await txn.insert(
          DatabaseHelper.tableEnrolledCourses,
          {
            DatabaseHelper.columnId: enrolledCourse.id,
            DatabaseHelper.columnUserId: userId,
            DatabaseHelper.columnCompletionStatus: enrolledCourse.completionStatus,
            DatabaseHelper.columnCertificate: enrolledCourse.certificate != null
                ? jsonEncode(enrolledCourse.certificate)
                : null,
            DatabaseHelper.columnEnrollmentDate: DateTime.now().toIso8601String(),
            DatabaseHelper.columnCreatedAt: DateTime.now().toIso8601String(),
            DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        // Delete existing enrolled modules for this course
        await txn.delete(
          DatabaseHelper.tableEnrolledModules,
          where: '${DatabaseHelper.columnEnrolledCourseId} = ?',
          whereArgs: [enrolledCourse.id],
        );

        // Insert enrolled modules
        for (var module in enrolledCourse.modules) {
          await txn.insert(
            DatabaseHelper.tableEnrolledModules,
            {
              DatabaseHelper.columnEnrolledCourseId: enrolledCourse.id,
              DatabaseHelper.columnModuleId: module.id,
              DatabaseHelper.columnModuleCompletionStatus: module.completionStatus ? 1 : 0,
              DatabaseHelper.columnModuleLock: module.lock ? 1 : 0,
              DatabaseHelper.columnModuleState: module.state != null
                  ? jsonEncode(module.state)
                  : null,
              DatabaseHelper.columnModuleDependentList: module.dependentModuleList.isNotEmpty
                  ? jsonEncode(module.dependentModuleList)
                  : null,
              DatabaseHelper.columnModuleContentType: module.content?.type,
              DatabaseHelper.columnModuleDownloadLink: module.content?.downloadLink,
              DatabaseHelper.columnModuleScormDataPath: module.content?.scormDataPath,
              DatabaseHelper.columnCreatedAt: DateTime.now().toIso8601String(),
              DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
            },
            conflictAlgorithm: ConflictAlgorithm.replace,
          );

          // If this is SCORM content and has state data, save to scorm_progress table
          if (module.content?.isScorm == true && module.state != null) {
            await _saveScormProgressData(txn, userId, module.id, module.state!, module.completionStatus);
          }
        }
      });

      LoggerService.debug('Successfully inserted enrolled course ${enrolledCourse.id} for user $userId');
      return enrolledCourse.id;
    } catch (e) {
      LoggerService.error('Error inserting enrolled course', e);
      return -1;
    }
  }

  /// Save SCORM progress data to scorm_progress table
  Future<void> _saveScormProgressData(
    Transaction txn,
    int userId,
    int moduleId,
    Map<String, dynamic> stateData,
    bool completionStatus
  ) async {
    try {
      final now = DateTime.now();

      // Create ScormProgress object
      final scormProgress = ScormProgress(
        userId: userId,
        moduleId: moduleId,
        scormData: stateData,
        lastAccessed: now,
        syncStatus: true, // Mark as synced since it came from server
        syncAttempts: 0,
        createdAt: now,
        updatedAt: now,
      );

      // Insert or replace SCORM progress data
      await txn.insert(
        DatabaseHelper.tableScormProgress,
        scormProgress.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      LoggerService.debug('Saved SCORM progress data for user $userId, module $moduleId');
    } catch (e) {
      LoggerService.error('Error saving SCORM progress data for module $moduleId', e);
      // Don't rethrow - we don't want to fail the entire transaction for this
    }
  }

  /// Insert multiple enrolled courses
  Future<bool> insertEnrolledCourses(List<EnrolledCourse> enrolledCourses, int userId) async {
    try {
      for (var course in enrolledCourses) {
        await insertEnrolledCourse(course, userId);
      }
      LoggerService.debug('Successfully inserted ${enrolledCourses.length} enrolled courses for user $userId');
      return true;
    } catch (e) {
      LoggerService.error('Error inserting enrolled courses', e);
      return false;
    }
  }

  /// Get all enrolled courses for a user
  Future<List<EnrolledCourse>> getEnrolledCourses(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      // Get enrolled courses for the user
      final List<Map<String, dynamic>> courseMaps = await db.query(
        DatabaseHelper.tableEnrolledCourses,
        where: '${DatabaseHelper.columnUserId} = ?',
        whereArgs: [userId],
        orderBy: '${DatabaseHelper.columnEnrollmentDate} DESC',
      );
      
      // Convert to EnrolledCourse objects
      return await Future.wait(courseMaps.map((courseMap) async {
        // Get enrolled modules for this course
        final List<Map<String, dynamic>> moduleMaps = await db.query(
          DatabaseHelper.tableEnrolledModules,
          where: '${DatabaseHelper.columnEnrolledCourseId} = ?',
          whereArgs: [courseMap[DatabaseHelper.columnId]],
        );
        
        // Convert modules
        final modules = moduleMaps.map((moduleMap) {
          return EnrolledModule(
            id: moduleMap[DatabaseHelper.columnModuleId] ?? 0,
            completionStatus: (moduleMap[DatabaseHelper.columnModuleCompletionStatus] ?? 0) == 1,
            lock: (moduleMap[DatabaseHelper.columnModuleLock] ?? 1) == 1,
            dependentModuleList: moduleMap[DatabaseHelper.columnModuleDependentList] != null
                ? List<String>.from(jsonDecode(moduleMap[DatabaseHelper.columnModuleDependentList]))
                : [],
            content: moduleMap[DatabaseHelper.columnModuleContentType] != null
                ? ModuleContent(
                    type: moduleMap[DatabaseHelper.columnModuleContentType] ?? '',
                    downloadLink: moduleMap[DatabaseHelper.columnModuleDownloadLink] ?? '',
                    scormDataPath: moduleMap[DatabaseHelper.columnModuleScormDataPath],
                  )
                : null,
            state: moduleMap[DatabaseHelper.columnModuleState] != null
                ? jsonDecode(moduleMap[DatabaseHelper.columnModuleState])
                : null,
          );
        }).toList();
        
        return EnrolledCourse(
          id: courseMap[DatabaseHelper.columnId] ?? 0,
          completionStatus: (courseMap[DatabaseHelper.columnCompletionStatus] ?? 0.0).toDouble(),
          certificate: courseMap[DatabaseHelper.columnCertificate] != null 
              ? jsonDecode(courseMap[DatabaseHelper.columnCertificate]) 
              : null,
          modules: modules,
        );
      }).toList());
    } catch (e) {
      LoggerService.error('Error getting enrolled courses for user $userId', e);
      return [];
    }
  }

  /// Get a specific enrolled course by course ID and user ID
  Future<EnrolledCourse?> getEnrolledCourse(int courseId, int userId) async {
    try {
      final db = await _dbHelper.database;
      
      // Get the enrolled course
      final List<Map<String, dynamic>> courseMaps = await db.query(
        DatabaseHelper.tableEnrolledCourses,
        where: '${DatabaseHelper.columnId} = ? AND ${DatabaseHelper.columnUserId} = ?',
        whereArgs: [courseId, userId],
      );
      
      if (courseMaps.isEmpty) {
        return null;
      }
      
      final courseMap = courseMaps.first;
      
      // Get enrolled modules for this course
      final List<Map<String, dynamic>> moduleMaps = await db.query(
        DatabaseHelper.tableEnrolledModules,
        where: '${DatabaseHelper.columnEnrolledCourseId} = ?',
        whereArgs: [courseId],
      );
      
      // Convert modules
      final modules = moduleMaps.map((moduleMap) {
        return EnrolledModule(
          id: moduleMap[DatabaseHelper.columnModuleId] ?? 0,
          completionStatus: (moduleMap[DatabaseHelper.columnModuleCompletionStatus] ?? 0) == 1,
          lock: (moduleMap[DatabaseHelper.columnModuleLock] ?? 1) == 1,
          dependentModuleList: moduleMap[DatabaseHelper.columnModuleDependentList] != null
              ? List<String>.from(jsonDecode(moduleMap[DatabaseHelper.columnModuleDependentList]))
              : [],
          content: moduleMap[DatabaseHelper.columnModuleContentType] != null
              ? ModuleContent(
                  type: moduleMap[DatabaseHelper.columnModuleContentType] ?? '',
                  downloadLink: moduleMap[DatabaseHelper.columnModuleDownloadLink] ?? '',
                  scormDataPath: moduleMap[DatabaseHelper.columnModuleScormDataPath],
                )
              : null,
          state: moduleMap[DatabaseHelper.columnModuleState] != null
              ? jsonDecode(moduleMap[DatabaseHelper.columnModuleState])
              : null,
        );
      }).toList();
      
      return EnrolledCourse(
        id: courseMap[DatabaseHelper.columnId] ?? 0,
        completionStatus: (courseMap[DatabaseHelper.columnCompletionStatus] ?? 0.0).toDouble(),
        certificate: courseMap[DatabaseHelper.columnCertificate] != null 
            ? jsonDecode(courseMap[DatabaseHelper.columnCertificate]) 
            : null,
        modules: modules,
      );
    } catch (e) {
      LoggerService.error('Error getting enrolled course $courseId for user $userId', e);
      return null;
    }
  }

  /// Update completion status for an enrolled course
  Future<bool> updateCourseCompletionStatus(int courseId, int userId, double completionStatus) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.update(
        DatabaseHelper.tableEnrolledCourses,
        {
          DatabaseHelper.columnCompletionStatus: completionStatus,
          DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
        },
        where: '${DatabaseHelper.columnId} = ? AND ${DatabaseHelper.columnUserId} = ?',
        whereArgs: [courseId, userId],
      );
      
      LoggerService.debug('Updated completion status for course $courseId to $completionStatus');
      return result > 0;
    } catch (e) {
      LoggerService.error('Error updating course completion status', e);
      return false;
    }
  }

  /// Update module completion status
  Future<bool> updateModuleCompletionStatus(int courseId, int moduleId, bool completionStatus) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.update(
        DatabaseHelper.tableEnrolledModules,
        {
          DatabaseHelper.columnModuleCompletionStatus: completionStatus ? 1 : 0,
          DatabaseHelper.columnUpdatedAt: DateTime.now().toIso8601String(),
        },
        where: '${DatabaseHelper.columnEnrolledCourseId} = ? AND ${DatabaseHelper.columnModuleId} = ?',
        whereArgs: [courseId, moduleId],
      );
      
      LoggerService.debug('Updated module $moduleId completion status to $completionStatus');
      return result > 0;
    } catch (e) {
      LoggerService.error('Error updating module completion status', e);
      return false;
    }
  }

  /// Delete enrolled course for a user
  Future<bool> deleteEnrolledCourse(int courseId, int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.delete(
        DatabaseHelper.tableEnrolledCourses,
        where: '${DatabaseHelper.columnId} = ? AND ${DatabaseHelper.columnUserId} = ?',
        whereArgs: [courseId, userId],
      );
      
      LoggerService.debug('Deleted enrolled course $courseId for user $userId');
      return result > 0;
    } catch (e) {
      LoggerService.error('Error deleting enrolled course', e);
      return false;
    }
  }

  /// Delete all enrolled courses for a user
  Future<bool> deleteAllEnrolledCourses(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.delete(
        DatabaseHelper.tableEnrolledCourses,
        where: '${DatabaseHelper.columnUserId} = ?',
        whereArgs: [userId],
      );
      
      LoggerService.debug('Deleted all enrolled courses for user $userId');
      return result >= 0;
    } catch (e) {
      LoggerService.error('Error deleting all enrolled courses', e);
      return false;
    }
  }

  /// Check if a course is enrolled for a user
  Future<bool> isEnrolled(int courseId, int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> result = await db.query(
        DatabaseHelper.tableEnrolledCourses,
        where: '${DatabaseHelper.columnId} = ? AND ${DatabaseHelper.columnUserId} = ?',
        whereArgs: [courseId, userId],
        limit: 1,
      );
      
      return result.isNotEmpty;
    } catch (e) {
      LoggerService.error('Error checking enrollment status', e);
      return false;
    }
  }

  /// Get count of enrolled courses for a user
  Future<int> getEnrolledCoursesCount(int userId) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseHelper.tableEnrolledCourses} WHERE ${DatabaseHelper.columnUserId} = ?',
        [userId],
      );
      
      return result.first['count'] as int? ?? 0;
    } catch (e) {
      LoggerService.error('Error getting enrolled courses count', e);
      return 0;
    }
  }
}
