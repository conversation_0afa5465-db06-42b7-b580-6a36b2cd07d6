import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/user_model.dart';
import '../services/logger_service.dart';

class UserRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // Insert or update a user in the database
  Future<int> saveUser(User user) async {
    try {
      final db = await _dbHelper.database;
      
      // Insert or replace user
      return await db.insert(
        DatabaseHelper.tableUsers,
        user.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      LoggerService.error('Error saving user', e);
      return -1;
    }
  }

  // Get the current user from the database
  Future<User?> getUser() async {
    try {
      final db = await _dbHelper.database;
      
      // Get all users (should be only one)
      final List<Map<String, dynamic>> maps = await db.query(DatabaseHelper.tableUsers);
      
      if (maps.isEmpty) {
        return null;
      }
      
      // Return the first user (there should only be one)
      return User.fromMap(maps.first);
    } catch (e) {
      LoggerService.error('Error getting user', e);
      return null;
    }
  }

  // Get a user by ID
  Future<User?> getUserById(int id) async {
    try {
      final db = await _dbHelper.database;
      
      // Get user by ID
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableUsers,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [id],
      );
      
      if (maps.isEmpty) {
        return null;
      }
      
      return User.fromMap(maps.first);
    } catch (e) {
      LoggerService.error('Error getting user by ID', e);
      return null;
    }
  }

  // Update user data
  Future<int> updateUser(User user) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.update(
        DatabaseHelper.tableUsers,
        user.toMap(),
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [user.id],
      );
    } catch (e) {
      LoggerService.error('Error updating user', e);
      return -1;
    }
  }

  // Update specific user fields
  Future<int> updateUserFields(int userId, Map<String, dynamic> fields) async {
    try {
      final db = await _dbHelper.database;
      
      // Add updated timestamp
      fields[DatabaseHelper.columnUpdatedAt] = DateTime.now().toIso8601String();
      
      return await db.update(
        DatabaseHelper.tableUsers,
        fields,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [userId],
      );
    } catch (e) {
      LoggerService.error('Error updating user fields', e);
      return -1;
    }
  }

  // Delete a user
  Future<int> deleteUser(int id) async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(
        DatabaseHelper.tableUsers,
        where: '${DatabaseHelper.columnId} = ?',
        whereArgs: [id],
      );
    } catch (e) {
      LoggerService.error('Error deleting user', e);
      return -1;
    }
  }

  // Delete all users
  Future<int> deleteAllUsers() async {
    try {
      final db = await _dbHelper.database;
      
      return await db.delete(DatabaseHelper.tableUsers);
    } catch (e) {
      LoggerService.error('Error deleting all users', e);
      return -1;
    }
  }

  // Check if a user exists
  Future<bool> userExists() async {
    try {
      final db = await _dbHelper.database;
      
      final count = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM ${DatabaseHelper.tableUsers}')
      );
      
      return count != null && count > 0;
    } catch (e) {
      LoggerService.error('Error checking if user exists', e);
      return false;
    }
  }
}
