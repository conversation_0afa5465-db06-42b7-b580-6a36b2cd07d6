import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../services/language_service.dart';
import '../services/logger_service.dart';
import '../services/theme_service.dart';

/// Provider to manage all app-wide settings
class AppSettingsProvider extends ChangeNotifier {
  // Theme settings
  bool _isDarkMode = false;
  ThemeMode _themeMode = ThemeMode.light;

  // Language settings
  Locale? _locale;

  // Callback for language change events
  Function(String)? _onLanguageChange;

  // Getters
  bool get isDarkMode => _isDarkMode;
  ThemeMode get themeMode => _themeMode;
  Locale? get locale => _locale;

  /// Set callback for language change events
  void setOnLanguageChangeCallback(Function(String)? callback) {
    _onLanguageChange = callback;
  }

  /// Initialize the provider with saved settings
  Future<void> init() async {
    try {
      // Initialize locale
      _locale = await LanguageService.getCurrentLocale();

      // Set initial language code in AppConfig
      if (_locale != null) {
        AppConfig.setLanguageCode(_locale!.languageCode);
      }

      // Initialize theme
      _isDarkMode = await ThemeService.isDarkMode();
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;

      LoggerService.debug('AppSettingsProvider initialized');
    } catch (e) {
      LoggerService.error('Error initializing AppSettingsProvider', e);
    }
  }

  /// Change the application locale
  Future<bool> setLocale(String languageCode) async {
    try {
      final success = await LanguageService.setLocale(languageCode);
      if (success) {
        _locale = Locale(languageCode);

        // Update AppConfig with new language code
        AppConfig.setLanguageCode(languageCode);

        // Call language change callback to reload data
        if (_onLanguageChange != null) {
          _onLanguageChange!(languageCode);
        }

        LoggerService.info('Locale changed to: $languageCode');
        notifyListeners();
        return true;
      } else {
        LoggerService.warning('Failed to change locale to: $languageCode');
        return false;
      }
    } catch (e) {
      LoggerService.error('Error setting locale', e);
      return false;
    }
  }

  /// Toggle between light and dark theme
  Future<bool> toggleThemeMode() async {
    try {
      _isDarkMode = !_isDarkMode;
      _themeMode = _isDarkMode ? ThemeMode.dark : ThemeMode.light;

      // Save theme preference
      await ThemeService.setDarkMode(_isDarkMode);

      LoggerService.info('Theme changed to: ${_isDarkMode ? 'dark' : 'light'}');
      notifyListeners();
      return true;
    } catch (e) {
      LoggerService.error('Error toggling theme mode', e);
      return false;
    }
  }

  /// Set specific theme mode
  Future<bool> setThemeMode(ThemeMode mode) async {
    try {
      _themeMode = mode;
      _isDarkMode = mode == ThemeMode.dark;

      // Save theme preference
      await ThemeService.setDarkMode(_isDarkMode);

      LoggerService.info('Theme mode set to: $mode');
      notifyListeners();
      return true;
    } catch (e) {
      LoggerService.error('Error setting theme mode', e);
      return false;
    }
  }
}
