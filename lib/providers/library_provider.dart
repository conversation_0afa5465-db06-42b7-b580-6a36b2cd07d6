import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/library_item_model.dart';
import '../services/library_service.dart';
import '../services/logger_service.dart';
import '../services/content_handler_service.dart';

/// Provider to manage digital library data and state
class LibraryProvider extends ChangeNotifier {
  List<LibraryItem> _items = [];
  List<LibraryItem> _filteredItems = [];
  bool _isLoading = false;
  String _error = '';
  String _searchQuery = '';
  String _selectedContentType = 'all';
  String _selectedCategory = 'all';
  bool _showDownloadedOnly = false;

  // View mode (grid or list)
  bool _isGridView = false; // Default to list view

  // Download progress tracking
  Map<int, double> _downloadProgress = {};
  int? _currentDownloadingItemId;

  // Getters
  List<LibraryItem> get items => _items;
  List<LibraryItem> get filteredItems => _filteredItems;
  bool get isLoading => _isLoading;
  String get error => _error;
  String get searchQuery => _searchQuery;
  String get selectedContentType => _selectedContentType;
  String get selectedCategory => _selectedCategory;
  bool get showDownloadedOnly => _showDownloadedOnly;
  bool get isGridView => _isGridView;

  // Download progress getters
  Map<int, double> get downloadProgress => _downloadProgress;
  int? get currentDownloadingItemId => _currentDownloadingItemId;

  // Get download progress for a specific item
  double getDownloadProgressForItem(int itemId) {
    return _downloadProgress[itemId] ?? 0.0;
  }

  // Check if an item is currently downloading
  bool isItemDownloading(int itemId) {
    return LibraryService.isDownloading(itemId);
  }

  // Get unique content types from items
  Set<String> get contentTypes {
    final types = <String>{};
    for (var item in _items) {
      if (item.contentType.isNotEmpty) {
        types.add(item.contentType);
      }
    }
    return types;
  }

  // Get unique categories from items
  Set<String> get categories {
    final cats = <String>{};
    for (var item in _items) {
      if (item.contentCategory != null && item.contentCategory!.isNotEmpty) {
        cats.add(item.contentCategory!);
      }
    }
    return cats;
  }

  // Get available content types for filtering (includes 'all' option)
  List<String> get availableContentTypes {
    final types = contentTypes.toList();
    types.sort();
    return ['all', ...types];
  }

  // Get available categories for filtering (includes 'all' option)
  List<String> get availableCategories {
    final cats = categories.toList();
    cats.sort();
    return ['all', ...cats];
  }

  /// Initialize the provider
  Future<void> init() async {
    // Request storage permissions
    await _requestStoragePermissions();

    // Verify downloaded items first to ensure consistency
    await LibraryService.verifyDownloadedItems();

    // Then fetch library items
    await fetchLibraryItems();
  }

  /// Initialize storage access
  Future<void> _requestStoragePermissions() async {
    try {
      // Check if we can access storage by trying to get the application documents directory
      final directory = await getApplicationDocumentsDirectory();
      if (directory != null) {
        LoggerService.debug(
            'Application documents directory initialized: ${directory.path}');
      } else {
        LoggerService.warning('Application documents directory not available');
      }
    } catch (e) {
      LoggerService.error(
          'Error initializing application documents directory access', e);
    }
  }

  /// Fetch library items from API or local database
  Future<void> fetchLibraryItems({bool forceRefresh = false}) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      LoggerService.debug(
          'LibraryProvider: Starting to fetch library items, forceRefresh=$forceRefresh');

      // Add timeout to prevent hanging indefinitely
      final items =
          await LibraryService.fetchLibraryItems(forceRefresh: forceRefresh)
              .timeout(
        const Duration(seconds: 120),
        onTimeout: () {
          LoggerService.warning(
              'LibraryProvider: Timeout while fetching library items');
          throw TimeoutException('Request timed out after 60 seconds');
        },
      );

      LoggerService.debug(
          'LibraryProvider: Successfully fetched ${items.length} library items');

      _items = items;
      _applyFilters();

      // If we're not forcing a refresh and items came from the database,
      // preload the images in the background for better performance
      if (!forceRefresh && items.isNotEmpty) {
        // No need to await, let it run in the background
        LoggerService.debug(
            'LibraryProvider: Starting background preload of thumbnail images');
        LibraryService.preloadThumbnailImages(items);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;

      // Provide more user-friendly error messages based on exception type
      if (e is TimeoutException) {
        _error =
            'Request timed out. Please check your internet connection and try again.';
      } else if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused')) {
        _error =
            'Unable to connect to the server. Please check your internet connection.';
      } else if (e.toString().contains('Authentication token')) {
        _error = 'Authentication error. Please log in again.';
      } else {
        _error = 'Failed to load library items: ${e.toString()}';
      }

      LoggerService.error(
          'LibraryProvider: Error fetching library items: $_error', e);
      notifyListeners();
    }
  }

  /// Get a library item by ID
  Future<LibraryItem?> getItemById(int id) async {
    try {
      // First check if the item is in the current list
      final itemInList = _items.where((item) => item.id == id).toList();
      if (itemInList.isNotEmpty) {
        return itemInList.first;
      }

      // If not found in the list, try to get it from the service
      return await LibraryService.getLibraryItemById(id);
    } catch (e) {
      LoggerService.error('Error getting library item by ID: $id', e);
      return null;
    }
  }

  /// Download a library item with progress tracking
  Future<bool> downloadItem(LibraryItem item) async {
    try {
      // Reset progress for this item
      _downloadProgress[item.id] = 0.0;
      _currentDownloadingItemId = item.id;
      notifyListeners();

      final success = await LibraryService.downloadLibraryItem(
        item,
        onProgress: (received, total) {
          if (total != -1) {
            // Calculate progress percentage (0.0 to 1.0)
            final progress = received / total;
            _downloadProgress[item.id] = progress;
            notifyListeners();
          }
        },
      );

      // Clear progress after download completes
      _downloadProgress.remove(item.id);
      if (_currentDownloadingItemId == item.id) {
        _currentDownloadingItemId = null;
      }

      if (success) {
        // Update the item in the list
        final index = _items.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          // Get the updated item from the database
          final updatedItem =
              await LibraryService.repository.getLibraryItemById(item.id);
          if (updatedItem != null) {
            _items[index] = updatedItem;
            _applyFilters();
          }
        }
      }

      notifyListeners();
      return success;
    } catch (e) {
      // Clear progress on error
      _downloadProgress.remove(item.id);
      if (_currentDownloadingItemId == item.id) {
        _currentDownloadingItemId = null;
      }

      // Provide more user-friendly error message
      if (e is FileSystemException) {
        _error = 'Failed to download item: Storage error';
        LoggerService.error(
            'FileSystemException while downloading: ${e.message}, path: ${e.path}',
            e);
      } else {
        _error = 'Failed to download item: ${e.toString()}';
        LoggerService.error(_error, e);
      }

      notifyListeners();
      return false;
    }
  }

  /// Delete a downloaded library item
  Future<bool> deleteDownloadedItem(LibraryItem item) async {
    try {
      _isLoading = true;
      notifyListeners();

      final success = await LibraryService.deleteDownloadedItem(item);

      if (success) {
        // Update the item in the list
        final index = _items.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          // Get the updated item from the database
          final updatedItem =
              await LibraryService.repository.getLibraryItemById(item.id);
          if (updatedItem != null) {
            _items[index] = updatedItem;
            _applyFilters();
          }
        }
      }

      _isLoading = false;
      notifyListeners();
      return success;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to delete downloaded item: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Open a library item
  Future<bool> openItem(LibraryItem item, [BuildContext? context]) async {
    try {
      // If context is provided, use ContentHandlerService to open the item
      if (context != null) {
        return await ContentHandlerService.openLibraryItem(
          context,
          item,
          onError: (errorMessage) {
            _error = errorMessage;
            notifyListeners();
          },
          showErrorSnackbar: false, // We'll handle snackbars in the UI
        );
      }

      // If no context is provided, we can only handle downloaded files
      if (item.isExternalLink) {
        // External links need a context to open in WebView
        LoggerService.info(
            'Cannot open external link without context: ${item.contentLink}');
        _error = 'Cannot open external link without UI context';
        notifyListeners();
        return false;
      }

      if (item.isDownloaded && item.localFilePath != null) {
        final file = File(item.localFilePath!);
        if (!await file.exists()) {
          // Update the database to reflect that the file is no longer available
          await LibraryService.repository
              .updateDownloadStatus(item.id, false, null);

          // Update the item in the list
          final index = _items.indexWhere((i) => i.id == item.id);
          if (index != -1) {
            _items[index] = _items[index]
                .copyWith(isDownloaded: false, localFilePath: null);
            _applyFilters();
          }

          _error = 'File not found. It may have been deleted.';
          notifyListeners();
          return false;
        }

        // Use ContentHandlerService to open the file
        return await ContentHandlerService.openLibraryItem(
          null, // No context needed for local files
          item,
          onError: (errorMessage) {
            _error = errorMessage;
            notifyListeners();
          },
          showErrorSnackbar: false,
        );
      }

      _error = 'File not downloaded';
      notifyListeners();
      return false;
    } catch (e) {
      _error = 'Failed to open item: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Search library items
  void search(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// Filter by content type
  void filterByContentType(String contentType) {
    _selectedContentType = contentType;
    _applyFilters();
    notifyListeners();
  }

  /// Filter by category
  void filterByCategory(String category) {
    _selectedCategory = category;
    _applyFilters();
    notifyListeners();
  }

  /// Toggle show downloaded only
  void toggleShowDownloadedOnly() {
    _showDownloadedOnly = !_showDownloadedOnly;
    _applyFilters();
    notifyListeners();
  }

  /// Apply all filters
  void _applyFilters() {
    // First filter the items based on criteria
    List<LibraryItem> filtered = _items.where((item) {
      // Filter by download status
      if (_showDownloadedOnly && !item.isDownloaded) {
        return false;
      }

      // Filter by content type
      if (_selectedContentType != 'all' &&
          item.contentType != _selectedContentType) {
        return false;
      }

      // Filter by category
      if (_selectedCategory != 'all' &&
          (item.contentCategory == null ||
              item.contentCategory != _selectedCategory)) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        return item.contentTitle
            .toLowerCase()
            .contains(_searchQuery.toLowerCase());
      }

      return true;
    }).toList();

    // Now separate downloaded and non-downloaded items
    List<LibraryItem> downloadedItems =
        filtered.where((item) => item.isDownloaded).toList();
    List<LibraryItem> nonDownloadedItems =
        filtered.where((item) => !item.isDownloaded).toList();

    // Sort each group by title for consistency
    downloadedItems.sort((a, b) => a.contentTitle.compareTo(b.contentTitle));
    nonDownloadedItems.sort((a, b) => a.contentTitle.compareTo(b.contentTitle));

    // Combine the lists with downloaded items first
    _filteredItems = [...downloadedItems, ...nonDownloadedItems];
  }

  /// Reset all filters
  void resetFilters() {
    _searchQuery = '';
    _selectedContentType = 'all';
    _selectedCategory = 'all';
    _showDownloadedOnly = false;
    _filteredItems = _items;
    notifyListeners();
  }

  /// Clear cache
  Future<void> clearCache({bool deleteFiles = false}) async {
    try {
      _isLoading = true;
      notifyListeners();

      await LibraryService.clearCache(deleteFiles: deleteFiles);

      // Refresh the data
      await fetchLibraryItems(forceRefresh: true);

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to clear cache: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
    }
  }

  /// Clear error message
  void clearError() {
    _error = '';
    notifyListeners();
  }

  /// Toggle between grid and list view
  void toggleViewMode() {
    _isGridView = !_isGridView;
    notifyListeners();
  }

  /// Set specific view mode
  void setViewMode(bool isGrid) {
    _isGridView = isGrid;
    notifyListeners();
  }

  /// Cancel a download in progress
  Future<bool> cancelDownload(LibraryItem item) async {
    final cancelled = LibraryService.cancelDownload(item.id);

    if (cancelled) {
      // Clear progress
      _downloadProgress.remove(item.id);
      if (_currentDownloadingItemId == item.id) {
        _currentDownloadingItemId = null;
      }

      // Set a flag to indicate this was a user-initiated cancellation
      // This helps distinguish between errors and user cancellations
      _error = 'USER_CANCELLED';

      notifyListeners();
    }

    return cancelled;
  }
}
