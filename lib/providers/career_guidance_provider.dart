import 'dart:math';
import 'package:flutter/material.dart';
import '../models/career_guidance_model.dart';
import '../models/question_model.dart';
import '../services/career_guidance_service.dart';
import '../services/logger_service.dart';

/// Provider to manage career guidance quiz state
class CareerGuidanceProvider extends ChangeNotifier {
  // Quiz data
  List<CareerQuestionCategory> _categories = [];
  List<CareerQuestion> _allQuestions = [];
  int _currentQuestionIndex = 0;

  // Quiz state
  bool _isLoading = false;
  bool _isSubmitting = false;
  String _error = '';
  bool _quizStarted = false;
  bool _quizCompleted = false;
  bool _shouldShowResults = false;

  // Results
  CareerGuidanceResult? _result;

  // Getters
  List<CareerQuestionCategory> get categories => _categories;
  List<CareerQuestion> get allQuestions => _allQuestions;
  int get currentQuestionIndex => _currentQuestionIndex;
  bool get isLoading => _isLoading;
  bool get isSubmitting => _isSubmitting;
  String get error => _error;
  bool get quizStarted => _quizStarted;
  bool get quizCompleted => _quizCompleted;
  bool get shouldShowResults => _shouldShowResults;
  CareerGuidanceResult? get result => _result;

  // Quiz progress
  CareerQuestion? get currentQuestion {
    if (_currentQuestionIndex < _allQuestions.length) {
      return _allQuestions[_currentQuestionIndex];
    }
    return null;
  }

  int get totalQuestions => _allQuestions.length;
  double get progressPercentage {
    if (totalQuestions == 0) return 0.0;
    return (_currentQuestionIndex / totalQuestions) * 100;
  }

  int get remainingQuestions => totalQuestions - _currentQuestionIndex;
  bool get hasNextQuestion => _currentQuestionIndex < _allQuestions.length - 1;
  bool get hasPreviousQuestion => _currentQuestionIndex > 0;

  /// Initialize the provider
  Future<void> init() async {
    await loadExistingResults();
  }

  /// Load results after user login
  Future<void> loadResultsAfterLogin() async {
    try {
      _isLoading = true;
      notifyListeners();

      final existingResult = await CareerGuidanceService.getExistingResults();

      if (existingResult != null) {
        _result = existingResult;
        _quizCompleted = true;
        // Don't auto-navigate after login - let user navigate manually
        _shouldShowResults = false;
        LoggerService.info(
            'Loaded career guidance results after login - no auto-navigation');
      } else {
        LoggerService.info(
            'No existing career guidance results found after login');
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      LoggerService.error('Error loading results after login', e);
      notifyListeners();
    }
  }

  /// Load questions from API
  Future<bool> loadQuestions() async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      final categories = await CareerGuidanceService.getQuestions();
      _categories = categories;

      // Flatten all questions into a single list
      _allQuestions = [];
      for (final category in categories) {
        for (final question in category.questions) {
          // Store category info with question for later scoring
          final questionWithCategory = CareerQuestion(
            id: question.id,
            statement: question.statement,
            image: question.image,
            answer: null,
          );
          // Add a way to track which category this question belongs to
          questionWithCategory._categoryName = category.name;
          _allQuestions.add(questionWithCategory);
        }
      }

      // Randomize question order
      _allQuestions.shuffle(Random());
      LoggerService.info(
          'Questions loaded and randomized: ${_allQuestions.length} total');

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load questions: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Start the quiz
  Future<bool> startQuiz() async {
    try {
      if (_allQuestions.isEmpty) {
        final success = await loadQuestions();
        if (!success) return false;
      }

      _quizStarted = true;
      _quizCompleted = false;
      _currentQuestionIndex = 0;
      _result = null;
      _error = '';

      // Reset all answers
      for (final question in _allQuestions) {
        question.answer = null;
      }

      // Randomize question order for each quiz attempt
      _allQuestions.shuffle(Random());
      LoggerService.info('Quiz started with randomized question order');

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'Failed to start quiz: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Answer current question and move to next
  Future<bool> answerQuestion(LikertScale answer) async {
    try {
      if (currentQuestion == null) return false;

      // Save answer
      currentQuestion!.answer = answer;

      // Move to next question
      if (hasNextQuestion) {
        _currentQuestionIndex++;
        notifyListeners();
        return true;
      } else {
        // Quiz completed, submit responses
        return await completeQuiz();
      }
    } catch (e) {
      _error = 'Failed to answer question: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Go to previous question
  void goToPreviousQuestion() {
    if (hasPreviousQuestion) {
      _currentQuestionIndex--;
      notifyListeners();
    }
  }

  /// Go to next question (without answering)
  void goToNextQuestion() {
    if (hasNextQuestion) {
      _currentQuestionIndex++;
      notifyListeners();
    }
  }

  /// Complete the quiz and submit responses
  Future<bool> completeQuiz() async {
    try {
      _isSubmitting = true;
      _error = '';
      notifyListeners();

      // Get all answered questions
      final answeredQuestions =
          _allQuestions.where((q) => q.answer != null).toList();

      if (answeredQuestions.isEmpty) {
        _error = 'No questions answered';
        _isSubmitting = false;
        notifyListeners();
        return false;
      }

      // Submit to API
      final result =
          await CareerGuidanceService.submitResponses(answeredQuestions);

      _result = result;
      _quizCompleted = true;
      _isSubmitting = false;
      notifyListeners();

      LoggerService.info('Quiz completed successfully');
      return true;
    } catch (e) {
      _isSubmitting = false;
      _error = 'Failed to submit quiz: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
      return false;
    }
  }

  /// Load existing results from API
  Future<void> loadExistingResults() async {
    try {
      _isLoading = true;
      notifyListeners();

      final existingResult = await CareerGuidanceService.getExistingResults();

      if (existingResult != null) {
        _result = existingResult;
        _quizCompleted = true;
        LoggerService.info('Loaded existing quiz results');
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      LoggerService.error('Error loading existing results', e);
      notifyListeners();
    }
  }

  /// Reset quiz to start over
  void resetQuiz() {
    _quizStarted = false;
    _quizCompleted = false;
    _currentQuestionIndex = 0;
    _result = null;
    _error = '';
    _shouldShowResults = false;

    // Reset all answers
    for (final question in _allQuestions) {
      question.answer = null;
    }

    notifyListeners();
  }

  /// Clear the shouldShowResults flag (called when results page is shown)
  void clearShouldShowResults() {
    _shouldShowResults = false;
    notifyListeners();
  }

  /// Clear error message
  void clearError() {
    _error = '';
    notifyListeners();
  }

  /// Clear all user-specific data (called when user logs out or new user logs in)
  void clearUserData() {
    LoggerService.info('Clearing career guidance user data');

    // Clear results and quiz state
    _result = null;
    _quizStarted = false;
    _quizCompleted = false;
    _shouldShowResults = false;
    _currentQuestionIndex = 0;
    _error = '';
    _isSubmitting = false;

    // Clear all question answers but keep the questions themselves
    for (final question in _allQuestions) {
      question.answer = null;
    }

    LoggerService.debug('Career guidance user data cleared');
    notifyListeners();
  }

  /// Get questions for a specific category
  List<CareerQuestion> getQuestionsForCategory(String categoryName) {
    return _allQuestions.where((q) => q._categoryName == categoryName).toList();
  }

  /// Get answered questions count
  int get answeredQuestionsCount {
    return _allQuestions.where((q) => q.answer != null).length;
  }

  /// Check if user can proceed to results
  bool get canProceedToResults {
    return answeredQuestionsCount >=
        totalQuestions * 0.8; // At least 80% answered
  }

  /// Reload data when language changes
  Future<void> reloadDataForLanguageChange(String newLanguageCode) async {
    try {
      LoggerService.info(
          'Reloading career guidance data for language: $newLanguageCode');

      // Clear current data
      _categories = [];
      _allQuestions = [];
      _currentQuestionIndex = 0;
      _result = null;
      _error = '';
      _quizStarted = false;
      _quizCompleted = false;
      _shouldShowResults = false;

      // Reload questions in new language
      await loadQuestions();

      // Reload existing results in new language if user has completed quiz
      await loadExistingResults();

      LoggerService.info(
          'Career guidance data reloaded for language: $newLanguageCode');
      notifyListeners();
    } catch (e) {
      LoggerService.error(
          'Error reloading career guidance data for language change', e);
      _error = 'Failed to reload data for language change';
      notifyListeners();
    }
  }
}

/// Extension to add category tracking to CareerQuestion
extension CareerQuestionExtension on CareerQuestion {
  static final Map<CareerQuestion, String> _categoryMap = {};

  String? get _categoryName => _categoryMap[this];
  set _categoryName(String? value) {
    if (value != null) {
      _categoryMap[this] = value;
    } else {
      _categoryMap.remove(this);
    }
  }
}
