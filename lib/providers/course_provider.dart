import 'package:flutter/material.dart';
import '../models/course_model.dart';
import '../services/course_service.dart';
import '../services/logger_service.dart';
import '../repositories/user_repository.dart';

/// Provider to manage courses data and state
class CourseProvider extends ChangeNotifier {
  List<Course> _courses = [];
  List<Course> _filteredCourses = [];
  List<Course> _enrolledCourses = [];
  List<EnrolledCourse> _enrolledCoursesData = [];
  bool _isLoading = false;
  bool _isLoadingEnrolled = false;
  String _error = '';
  String _enrolledError = '';
  Set<String> _availableLanguages = {};
  Set<String> _availableTags = {};

  // Selected filters
  String _selectedLanguage = 'all';
  String _selectedTag = 'all';
  String _searchQuery = '';

  // View mode (grid or list)
  bool _isGridView = false; // Set to false for list view as default

  // Getters
  List<Course> get courses => _courses;
  List<Course> get filteredCourses => _filteredCourses;
  List<Course> get enrolledCourses => _enrolledCourses;
  List<EnrolledCourse> get enrolledCoursesData => _enrolledCoursesData;
  bool get isLoading => _isLoading;
  bool get isLoadingEnrolled => _isLoadingEnrolled;
  String get error => _error;
  String get enrolledError => _enrolledError;
  Set<String> get availableLanguages => _availableLanguages;
  Set<String> get availableTags => _availableTags;
  String get selectedLanguage => _selectedLanguage;
  String get selectedTag => _selectedTag;
  String get searchQuery => _searchQuery;
  bool get isGridView => _isGridView;

  /// Initialize the provider
  Future<void> init() async {
    await fetchCourses();
  }

  /// Fetch courses from API or local database
  Future<void> fetchCourses({bool forceRefresh = false}) async {
    try {
      _isLoading = true;
      _error = '';
      notifyListeners();

      final courses = await CourseService.fetchCourses(forceRefresh: forceRefresh);

      _courses = courses;
      _filteredCourses = courses;

      // Extract available filter options
      _availableLanguages = {};
      _availableTags = {};

      for (var course in courses) {
        // Add non-empty languages and ignore "all" to avoid conflicts with filter UI
        if (course.language.isNotEmpty && course.language.toLowerCase() != "all") {
          _availableLanguages.add(course.language);
        }

        for (var tag in course.tags) {
          // Ignore empty tags and the tag "all" to avoid conflicts with filter UI
          if (tag.isNotEmpty && tag.toLowerCase() != "all") {
            _availableTags.add(tag);
          }
        }
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _error = 'Failed to load courses: ${e.toString()}';
      LoggerService.error(_error, e);
      notifyListeners();
    }
  }

  /// Get a course by ID
  Future<Course?> getCourseById(int id) async {
    try {
      // First check if the course is in the current list
      final courseInList = _courses.where((course) => course.id == id).toList();
      if (courseInList.isNotEmpty) {
        return courseInList.first;
      }

      // If not found in the list, try to get it from the service
      return await CourseService.getCourseById(id);
    } catch (e) {
      LoggerService.error('Error getting course by ID: $id', e);
      return null;
    }
  }

  /// Search courses by title and description
  void search(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  /// Apply filters to courses
  void applyFilters({String? language, String? tag}) {
    if (language != null) {
      _selectedLanguage = language;
    }

    if (tag != null) {
      _selectedTag = tag;
    }

    _applyFilters();
  }

  /// Apply all filters (internal method)
  void _applyFilters() {
    _filteredCourses = _courses.where((course) {
      // Apply language filter
      bool matchesLanguage = _selectedLanguage.toLowerCase() == 'all' ||
                            course.language.toLowerCase() == _selectedLanguage.toLowerCase();

      // Apply tag filter
      bool matchesTag = _selectedTag.toLowerCase() == 'all' ||
                        course.tags.any((t) => t.toLowerCase() == _selectedTag.toLowerCase());

      // Apply search filter
      bool matchesSearch = _searchQuery.isEmpty ||
                          course.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                          course.description.toLowerCase().contains(_searchQuery.toLowerCase());

      return matchesLanguage && matchesTag && matchesSearch;
    }).toList();

    notifyListeners();
  }

  /// Reset all filters
  void resetFilters() {
    _selectedLanguage = 'all';
    _selectedTag = 'all';
    _searchQuery = '';
    _filteredCourses = _courses;
    notifyListeners();
  }

  /// Toggle between grid and list view
  void toggleViewMode() {
    _isGridView = !_isGridView;
    notifyListeners();
  }

  /// Set specific view mode
  void setViewMode(bool isGrid) {
    _isGridView = isGrid;
    notifyListeners();
  }

  /// Fetch enrolled courses from API or local database
  Future<void> fetchEnrolledCourses({bool forceRefresh = false, BuildContext? context}) async {
    try {
      _isLoadingEnrolled = true;
      _enrolledError = '';
      notifyListeners();

      // Fetch enrolled courses with full details
      final enrolledCourses = await CourseService.getEnrolledCoursesWithDetails(
        forceRefresh: forceRefresh,
        context: context
      );

      // Also fetch the raw enrolled course data for completion status
      final enrolledCoursesData = await CourseService.fetchEnrolledCourses(
        forceRefresh: forceRefresh,
        context: context
      );

      _enrolledCourses = enrolledCourses;
      _enrolledCoursesData = enrolledCoursesData;

      _isLoadingEnrolled = false;
      notifyListeners();
    } catch (e) {
      _isLoadingEnrolled = false;

      // Provide more user-friendly error messages based on exception type
      if (e.toString().contains('No user logged in and device is offline')) {
        _enrolledError = 'Please log in to view your enrolled courses.';
      } else if (e.toString().contains('Authentication required')) {
        _enrolledError = 'Please log in again to access your enrolled courses.';
      } else if (e.toString().contains('timed out') || e.toString().contains('SocketException')) {
        _enrolledError = 'Unable to connect. Showing offline data if available.';
      } else {
        _enrolledError = 'Failed to load enrolled courses: ${e.toString()}';
      }

      LoggerService.error(_enrolledError, e);
      notifyListeners();
    }
  }

  /// Fetch enrolled courses without context (for use in callbacks)
  /// This method will try to get user ID from UserRepository directly
  Future<void> fetchEnrolledCoursesWithoutContext({bool forceRefresh = false}) async {
    try {
      _isLoadingEnrolled = true;
      _enrolledError = '';
      notifyListeners();

      // Get user ID directly from UserRepository
      final userRepository = UserRepository();
      final user = await userRepository.getUser();

      if (user == null) {
        _enrolledError = 'No user logged in';
        _isLoadingEnrolled = false;
        notifyListeners();
        return;
      }

      // Fetch enrolled courses using the direct service methods
      final enrolledCoursesData = await CourseService.fetchEnrolledCoursesDirectly(
        forceRefresh: forceRefresh,
        userId: user.id
      );

      // Get enrolled courses with details
      final enrolledCourses = await CourseService.getEnrolledCoursesWithDetailsDirectly(
        forceRefresh: forceRefresh,
        userId: user.id
      );

      _enrolledCourses = enrolledCourses;
      _enrolledCoursesData = enrolledCoursesData;

      _isLoadingEnrolled = false;
      notifyListeners();
    } catch (e) {
      _isLoadingEnrolled = false;
      _enrolledError = 'Failed to load enrolled courses: ${e.toString()}';
      LoggerService.error(_enrolledError, e);
      notifyListeners();
    }
  }

  /// Get completion status for an enrolled course
  double getCompletionStatus(int courseId) {
    try {
      final enrolledCourse = _enrolledCoursesData.firstWhere(
        (course) => course.id == courseId,
        orElse: () => EnrolledCourse(id: -1, completionStatus: 0.0, modules: []),
      );
      return enrolledCourse.id != -1 ? enrolledCourse.completionStatus : 0.0;
    } catch (e) {
      LoggerService.error('Error getting completion status for course $courseId', e);
      return 0.0;
    }
  }

  /// Get certificate ID for an enrolled course
  int? getCertificateId(int courseId) {
    try {
      final enrolledCourse = _enrolledCoursesData.firstWhere(
            (course) => course.id == courseId,
        orElse: () => EnrolledCourse(id: -1, completionStatus: 0.0, modules: []),
      );

      if (enrolledCourse.id == -1 || enrolledCourse.certificate == null) {
        return null;
      }

      return enrolledCourse.certificate!['id'] as int?;
    } catch (e) {
      LoggerService.error('Error getting certificate ID for course $courseId', e);
      return null;
    }
  }

  /// Check if a course is enrolled
  bool isCourseEnrolled(int courseId) {
    return _enrolledCourses.any((course) => course.id == courseId);
  }

  /// Check if a module is locked by its ID
  bool isModuleLocked(int moduleId) {
    try {
      for (var course in _enrolledCoursesData) {
        final module = course.modules.firstWhere(
              (m) => m.id == moduleId,
          orElse: () => EnrolledModule(id: -1, completionStatus: false, lock: true),
        );
        if (module.id != -1) {
          return module.lock ?? false;
        }
      }
    } catch (e) {
      LoggerService.error('Error checking lock status for module $moduleId', e);
    }
    return true;
  }

  /// Check if a module is completed by its ID
  bool isModuleCompleted(int moduleId) {
    try {
      for (var course in _enrolledCoursesData) {
        final module = course.modules.firstWhere(
              (m) => m.id == moduleId,
          orElse: () => EnrolledModule(id: -1, completionStatus: false, lock: true),
        );
        if (module.id != -1) {
          return module.completionStatus;
        }
      }
    } catch (e) {
      LoggerService.error('Error checking completion status for module $moduleId', e);
    }
    return false;
  }



  /// Clear the course cache
  Future<void> clearCache({bool clearDatabase = false}) async {
    await CourseService.clearCache(clearDatabase: clearDatabase);
    await fetchCourses(forceRefresh: true);
  }

  /// Clear enrolled courses cache
  Future<void> clearEnrolledCoursesCache({bool clearDatabase = false, int? userId, BuildContext? context}) async {
    await CourseService.clearEnrolledCoursesCache(clearDatabase: clearDatabase, userId: userId);
    await fetchEnrolledCourses(forceRefresh: true, context: context);
  }

  /// Clear all user-specific data when user logs out
  void clearUserData() {
    _enrolledCourses = [];
    _enrolledCoursesData = [];
    _enrolledError = '';
    notifyListeners();
  }
}
