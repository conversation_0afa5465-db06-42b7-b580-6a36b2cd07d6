import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import '../../providers/user_provider.dart';
import '../../services/logger_service.dart';
import '../../services/module_progress_service.dart';
import '../../localization/app_localizations_extension.dart';
import '../../theme/app_theme.dart';

class PDFViewerScreen extends StatefulWidget {
  final String pdfUrl;
  final String title;
  final int? moduleId; // Optional module ID for tracking completion

  const PDFViewerScreen({
    super.key,
    required this.pdfUrl,
    required this.title,
    this.moduleId,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  late PdfViewerController controller;
  bool isLoading = true;
  bool hasError = false;
  String? errorMessage;
  int currentPageNumber = 1;
  int totalPages = 0;
  bool _hasMarkedAsAccessed = false;
  bool _hasMarkedAsCompleted = false;
  Set<int> _viewedPages = {};

  @override
  void initState() {
    super.initState();
    controller = PdfViewerController();
    LoggerService.info('Loading PDF directly from URL: ${widget.pdfUrl}');
    _markAsAccessed();
  }

  /// Mark module as accessed when PDF is opened
  Future<void> _markAsAccessed() async {
    if (widget.moduleId != null && !_hasMarkedAsAccessed) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;

        if (user != null) {
          await ModuleProgressService.markAsAccessed(
            userId: user.id,
            moduleId: widget.moduleId!,
            contentType: 'PDF',
          );
          _hasMarkedAsAccessed = true;
          LoggerService.debug('Marked PDF module ${widget.moduleId} as accessed');
        }
      } catch (e) {
        LoggerService.error('Error marking PDF module as accessed', e);
      }
    }
  }

  /// Build the appropriate PDF viewer based on URL type
  Widget _buildPdfViewer() {
    final isLocalFile = widget.pdfUrl.startsWith('file://');

    if (isLocalFile) {
      // For local files, use SfPdfViewer.file()
      final filePath = widget.pdfUrl.replaceFirst('file://', '');
      LoggerService.info('Loading local PDF file: $filePath');

      return SfPdfViewer.file(
        File(filePath),
        controller: controller,
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
      );
    } else {
      // For network URLs, use SfPdfViewer.network()
      LoggerService.info('Loading PDF from network: ${widget.pdfUrl}');

      return SfPdfViewer.network(
        widget.pdfUrl,
        controller: controller,
        onDocumentLoaded: _onDocumentLoaded,
        onDocumentLoadFailed: _onDocumentLoadFailed,
        onPageChanged: _onPageChanged,
      );
    }
  }

  /// Handle document loaded event
  void _onDocumentLoaded(PdfDocumentLoadedDetails details) {
    setState(() {
      totalPages = details.document.pages.count;
      isLoading = false;
    });
    LoggerService.info('PDF loaded successfully. Total pages: $totalPages');

    // For single-page PDFs, mark as completed immediately when loaded
    if (totalPages == 1) {
      _viewedPages.add(1);
      _checkCompletion();
    }
  }

  /// Handle document load failed event
  void _onDocumentLoadFailed(PdfDocumentLoadFailedDetails details) {
    setState(() {
      isLoading = false;
      hasError = true;
      errorMessage = details.error;
    });
    LoggerService.error('PDF load failed: ${details.error}', null);
  }

  /// Handle page changed event
  void _onPageChanged(PdfPageChangedDetails details) {
    setState(() {
      currentPageNumber = details.newPageNumber;
      _viewedPages.add(details.newPageNumber);
    });

    // Check if module should be marked as completed
    _checkCompletion();
  }

  /// Check if PDF should be marked as completed
  void _checkCompletion() {
    if (widget.moduleId != null && !_hasMarkedAsCompleted && totalPages > 0) {
      bool shouldComplete = false;

      if (totalPages == 1) {
        // For single-page PDFs, mark as completed when the page is viewed
        shouldComplete = _viewedPages.contains(1);
      } else {
        // For multi-page PDFs, mark as completed if user has viewed at least 80% of pages
        final viewedPercentage = _viewedPages.length / totalPages;
        shouldComplete = viewedPercentage >= 0.8;
      }

      if (shouldComplete) {
        _markAsCompleted();
      }
    }
  }

  /// Mark module as completed
  Future<void> _markAsCompleted() async {
    if (widget.moduleId != null && !_hasMarkedAsCompleted) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;

        if (user != null) {
          await ModuleProgressService.markAsCompleted(
            userId: user.id,
            moduleId: widget.moduleId!,
            contentType: 'PDF',
            progressData: {
              'total_pages': totalPages,
              'viewed_pages': _viewedPages.length,
              'completion_percentage': _viewedPages.length / totalPages,
            },
          );
          _hasMarkedAsCompleted = true;
          LoggerService.debug('Marked PDF module ${widget.moduleId} as completed');

          // Show completion message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Module completed!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      } catch (e) {
        LoggerService.error('Error marking PDF module as completed', e);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: Text(
          widget.title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (totalPages > 0)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  '$currentPageNumber / $totalPages',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('library.pdf_load_error'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                errorMessage ?? context.tr('library.unknown_error'),
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    hasError = false;
                    isLoading = true;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(context.tr('common.retry')),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        _buildPdfViewer(),
        if (isLoading)
          Container(
            color: AppTheme.backgroundColor,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.tr('library.loading_pdf'),
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
