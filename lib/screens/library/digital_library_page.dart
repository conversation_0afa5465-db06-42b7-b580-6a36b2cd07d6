import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/app_config.dart';
import '../../localization/app_localizations_extension.dart';
import '../../models/library_item_model.dart';
import '../../providers/library_provider.dart';
import '../../services/content_handler_service.dart';
import '../../theme/app_theme.dart';
import '../../theme/app_theme_extensions.dart';
import '../../widgets/universal_library_card.dart';
import 'package:flutter/cupertino.dart';


class DigitalLibraryPage extends StatefulWidget {
  const DigitalLibraryPage({Key? key}) : super(key: key);

  @override
  _DigitalLibraryPageState createState() => _DigitalLibraryPageState();
}

class _DigitalLibraryPageState extends State<DigitalLibraryPage> with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  bool _showFilters = false;

  // Override wantKeepAlive to keep this page alive when not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Initialize the library provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<LibraryProvider>(context, listen: false).init();
    });

    // Add listener to search controller to update the UI when text changes
    _searchController.addListener(() {
      setState(() {
        // This will rebuild the UI to show/hide the clear button
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to maintain the AutomaticKeepAliveClientMixin
    super.build(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('library.digital_library')),
        toolbarHeight: 0,
        elevation: 0,
      ),
      body: Consumer<LibraryProvider>(
        builder: (context, libraryProvider, child) {
          if (libraryProvider.isLoading && libraryProvider.items.isEmpty) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (libraryProvider.error.isNotEmpty && libraryProvider.items.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    libraryProvider.error,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    onPressed: () => libraryProvider.fetchLibraryItems(forceRefresh: true),
                    child: Text(context.tr('errors.try_again')),
                  ),
                ],
              ),
            );
          }

          if (libraryProvider.items.isEmpty) {
            return Center(
              child: Text(context.tr('library.no_items')),
            );
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search bar
              _buildSearchBar(libraryProvider),

              // Filter options
              if (_showFilters)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: _buildFilterOptions(libraryProvider),
                ),

              // Library items
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: _buildLibraryItemsList(libraryProvider),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterOptions(LibraryProvider libraryProvider) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content type filter
          if (libraryProvider.contentTypes.isNotEmpty)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.format_list_bulleted,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      context.tr('library.content_type'),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    FilterChip(
                      label: Text(
                        context.tr('library.all'),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: libraryProvider.selectedContentType == 'all' ? FontWeight.bold : FontWeight.normal,
                          color: libraryProvider.selectedContentType == 'all'
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      selected: libraryProvider.selectedContentType == 'all',
                      onSelected: (selected) {
                        if (selected) {
                          libraryProvider.filterByContentType('all');
                        }
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary,
                      showCheckmark: false,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    ...libraryProvider.contentTypes.map((type) => FilterChip(
                      label: Text(
                        type,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: libraryProvider.selectedContentType == type ? FontWeight.bold : FontWeight.normal,
                          color: libraryProvider.selectedContentType == type
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      selected: libraryProvider.selectedContentType == type,
                      onSelected: (selected) {
                        libraryProvider.filterByContentType(selected ? type : 'all');
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary,
                      showCheckmark: false,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    )).toList(),
                  ],
                ),
              ],
            ),

          // Category filter
          if (libraryProvider.categories.isNotEmpty) ...[
            const SizedBox(height: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.category,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      context.tr('library.category'),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    FilterChip(
                      label: Text(
                        context.tr('library.all'),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: libraryProvider.selectedCategory == 'all' ? FontWeight.bold : FontWeight.normal,
                          color: libraryProvider.selectedCategory == 'all'
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      selected: libraryProvider.selectedCategory == 'all',
                      onSelected: (selected) {
                        if (selected) {
                          libraryProvider.filterByCategory('all');
                        }
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary,
                      showCheckmark: false,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    ...libraryProvider.categories.map((category) => FilterChip(
                      label: Text(
                        category,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: libraryProvider.selectedCategory == category ? FontWeight.bold : FontWeight.normal,
                          color: libraryProvider.selectedCategory == category
                              ? Theme.of(context).colorScheme.onPrimary
                              : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      selected: libraryProvider.selectedCategory == category,
                      onSelected: (selected) {
                        libraryProvider.filterByCategory(selected ? category : 'all');
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary,
                      showCheckmark: false,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    )).toList(),
                  ],
                ),
              ],
            ),
          ],

          const SizedBox(height: 12),

          // Downloaded only filter
          Row(
            children: [
              Switch(
                value: libraryProvider.showDownloadedOnly,
                onChanged: (value) {
                  libraryProvider.toggleShowDownloadedOnly();
                },
              ),
              const SizedBox(width: 8),
              Text(
                context.tr('library.show_downloaded_only'),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Reset filters button
          if (libraryProvider.selectedContentType != 'all' ||
              libraryProvider.selectedCategory != 'all' ||
              libraryProvider.showDownloadedOnly ||
              libraryProvider.searchQuery.isNotEmpty)
            Center(
              child: TextButton.icon(
                onPressed: () {
                  // Clear the search field
                  _searchController.clear();
                  // Reset all filters in the provider
                  libraryProvider.resetFilters();
                  // Unfocus the text field
                  FocusScope.of(context).unfocus();
                },
                icon: const Icon(Icons.refresh, size: 16),
                label: Text(context.tr('library.reset_filters')),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLibraryItemsList(LibraryProvider libraryProvider) {
    // Separate downloaded and non-downloaded items
    final downloadedItems = libraryProvider.filteredItems.where((item) => item.isDownloaded).toList();
    final nonDownloadedItems = libraryProvider.filteredItems.where((item) => !item.isDownloaded).toList();

    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine grid layout based on screen width
        final screenWidth = constraints.maxWidth;
        int crossAxisCount = 2; // Minimum 2 cards per row

        // Adjust grid columns based on screen width
        if (screenWidth > 900) {
          crossAxisCount = 4; // Large tablets/desktop
        } else if (screenWidth > 600) {
          crossAxisCount = 3; // Tablets
        }

        // Calculate child aspect ratio for list view
        final listAspectRatio = crossAxisCount == 1 ? 4.0 : (crossAxisCount == 2 ? 3.5 : 3.2);

        // Aspect ratio for grid view (vertical cards)
        // Using the standard aspect ratio from AppTheme
        final gridAspectRatio = AppTheme.cardAspectRatio;

        return ListView(
          children: [
            // Downloaded items section (if any)
            if (downloadedItems.isNotEmpty) ...[
              Container(
                margin: const EdgeInsets.only(bottom: 4.0),
                padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
                decoration: BoxDecoration(
                  color: AppTheme.successColor.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.shadow.withOpacity(0.03),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.download_done,
                      size: 14,
                      color: Colors.green[700],
                    ),
                    const SizedBox(width: 6),
                    Text(
                      context.tr('library.downloaded_items'),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[800],
                        fontSize: 13,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${downloadedItems.length}',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[800],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Choose between grid view and list view based on the provider setting
              libraryProvider.isGridView
                  ? GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 8,
                        childAspectRatio: gridAspectRatio, // Adjusted for vertical card layout
                      ),
                      itemCount: downloadedItems.length,
                      itemBuilder: (context, index) {
                        final item = downloadedItems[index];
                        return _buildGridLibraryItem(item, libraryProvider);
                      },
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: downloadedItems.length,
                      itemBuilder: (context, index) {
                        final item = downloadedItems[index];
                        return Dismissible(
                          key: Key('library_item_${item.id}'),
                          direction: DismissDirection.endToStart,
                          background: Container(
                            alignment: Alignment.centerRight,
                            padding: const EdgeInsets.only(right: 16.0),
                            margin: const EdgeInsets.only(top: 4.0, bottom: 4.0),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.primary,
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).colorScheme.primary.withOpacity(0.08),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  context.tr('common.delete'),
                                  style: TextStyle(
                                    color: Theme.of(context).colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 11, // Even smaller font size
                                  ),
                                ),
                                const SizedBox(width: 3), // Further reduced spacing
                                Icon(
                                  Icons.delete_outline,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  size: 14, // Even smaller icon
                                ),
                              ],
                            ),
                          ),
                          // Don't remove the item, just handle the delete action
                          onDismissed: null,
                          dismissThresholds: const {DismissDirection.endToStart: 0.6},
                          confirmDismiss: (direction) async {
                            // Show delete confirmation and handle deletion
                            await _handleDeleteDownload(item, libraryProvider);
                            // Always return false to prevent actual dismissal
                            return false;
                          },
                          child: _buildLibraryItemCard(item, libraryProvider),
                        );
                      },
                    ),
            ],

            // Non-downloaded items section (if any)
            if (nonDownloadedItems.isNotEmpty) ...[
              Container(
                margin: const EdgeInsets.only(bottom: 6.0),
                padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.03),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.cloud_download_outlined,
                      size: 14,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      context.tr('library.available_items'),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                        fontSize: 13,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${nonDownloadedItems.length}',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Choose between grid view and list view based on the provider setting
              libraryProvider.isGridView
                  ? GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 8,
                        childAspectRatio: gridAspectRatio, // Adjusted for vertical card layout
                      ),
                      itemCount: nonDownloadedItems.length,
                      itemBuilder: (context, index) {
                        final item = nonDownloadedItems[index];
                        return _buildGridLibraryItem(item, libraryProvider);
                      },
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: nonDownloadedItems.length,
                      itemBuilder: (context, index) {
                        final item = nonDownloadedItems[index];
                        return _buildLibraryItemCard(item, libraryProvider);
                      },
                    ),
            ],

            // No items message
            if (libraryProvider.filteredItems.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Text(context.tr('library.no_matching_items')),
                ),
              ),
          ],
        );
      },
    );
  }

  // Build a grid-style library item (vertical card like in course details)
  Widget _buildGridLibraryItem(LibraryItem item, LibraryProvider libraryProvider) {
    return UniversalLibraryCard(
      item: item,
      isHorizontal: false,
      onDownload: (item) => _showDownloadConfirmation(item, libraryProvider),
      onOpen: (item) async {
        // Use ContentHandlerService to open the library item
        final success = await ContentHandlerService.openLibraryItem(
          context,
          item,
          showErrorSnackbar: true,
        );

        // If the item is not available for offline viewing and doesn't have a content link,
        // try to download it
        if (!success && !item.isDownloaded && item.contentLink.isEmpty) {
          if (mounted) {
            _showDownloadConfirmation(item, libraryProvider);
          }
        }
      },
      // Add delete functionality for downloaded items
      onDelete: item.isDownloaded ? (item) => _handleDeleteDownload(item, libraryProvider) : null,
    );
  }

  // Build a list-style library item (horizontal card)
  Widget _buildLibraryItemCard(LibraryItem item, LibraryProvider libraryProvider) {
    return UniversalLibraryCard(
      item: item,
      isHorizontal: true,
      onDownload: (item) => _showDownloadConfirmation(item, libraryProvider),
      onOpen: (item) async {
        // Use ContentHandlerService to open the library item
        final success = await ContentHandlerService.openLibraryItem(
          context,
          item,
          showErrorSnackbar: true,
        );

        // If the item is not available for offline viewing and doesn't have a content link,
        // try to download it
        if (!success && !item.isDownloaded && item.contentLink.isEmpty) {
          if (mounted) {
            _showDownloadConfirmation(item, libraryProvider);
          }
        }
      },
      // Add delete functionality for downloaded items
      onDelete: item.isDownloaded ? (item) => _handleDeleteDownload(item, libraryProvider) : null,
    );
  }

  // Handle deletion of downloaded content without removing the item
  Future<void> _handleDeleteDownload(LibraryItem item, LibraryProvider libraryProvider) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.delete_outline,
              color: Colors.red.shade700,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              context.tr('library.delete_confirmation_title'),
              style: TextStyle(
                color: Colors.red.shade700,
              ),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // File icon
            Container(
              width: 60,
              height: 60,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              child: Icon(
                _getContentTypeIcon(item),
                size: 30,
                color: Colors.red.shade300,
              ),
            ),

            // File title
            Text(
              item.contentTitle,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Confirmation message
            Text(
              context.tr('library.delete_confirmation_message'),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(context.tr('common.cancel')),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context, true),
            icon: const Icon(Icons.delete_outline, size: 18),
            label: Text(context.tr('common.delete')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            ),
          ),
        ],
        actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );

    if (confirmed == true) {
      // Show a progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.red.shade300),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                context.tr('library.deleting'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );

      try {
        final success = await libraryProvider.deleteDownloadedItem(item);

        // Close the progress dialog
        if (mounted) {
          Navigator.of(context).pop();

          // Show success or error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    success ? Icons.check_circle : Icons.error,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    success
                        ? context.tr('library.delete_success')
                        : context.tr('library.delete_error'),
                  ),
                ],
              ),
              backgroundColor: success ? Colors.green : Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              margin: const EdgeInsets.all(8),
            ),
          );
        }
      } catch (e) {
        // Close the progress dialog on error
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.error,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(context.tr('library.delete_error')),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              margin: const EdgeInsets.all(8),
            ),
          );
        }
      }
    }
  }

  // Show download confirmation with progress
  Future<void> _showDownloadConfirmation(LibraryItem item, LibraryProvider libraryProvider) async {
    // Check if this is an external link item
    if (item.isExternalLink) {
      // Use ContentHandlerService to open the external link
      if (mounted) {
        ContentHandlerService.openLibraryItem(context, item);
      }
      return;
    }

    // If library_downloadable is false, open content directly in WebView without downloading
    if (!AppConfig.libraryDownloadable && !item.contentFile.isEmpty) {
      if (mounted) {
        ContentHandlerService.openLibraryItem(context, item);
      }
      return;
    }

    // Get content type color for styling
    final contentTypeColor = _getContentTypeColor(item);
    final iconData = _getContentTypeIcon(item);

    // First show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // File title
            Text(
              item.contentTitle,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // File type
            Text(
              item.contentType,
              style: TextStyle(
                color: contentTypeColor,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Confirmation message
            Text(
              context.tr('library.download_confirmation_message'),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(context.tr('common.cancel')),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context, true),
            icon: const Icon(Icons.download, size: 18),
            label: Text(context.tr('library.download')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            ),
          ),
        ],
        actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );

    if (confirmed == true) {
      // Show progress dialog
      if (!mounted) return;

      // Create a stateful progress dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) {
          return Consumer<LibraryProvider>(
            builder: (context, libraryProvider, child) {
              // Get the current download progress
              final progress = libraryProvider.getDownloadProgressForItem(item.id);
              final progressPercent = (progress * 100).toInt();

              return AlertDialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // File icon
                    Container(
                      width: 60,
                      height: 60,
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: contentTypeColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Icon(
                            iconData,
                            size: 30,
                            color: contentTypeColor.withOpacity(0.5),
                          ),
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              value: progress > 0 ? progress : null, // Use determinate progress when available
                              valueColor: AlwaysStoppedAnimation<Color>(contentTypeColor),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // File title
                    Text(
                      item.contentTitle,
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // Progress percentage
                    if (progress > 0)
                      Text(
                        '$progressPercent%',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),

                    const SizedBox(height: 8),

                    // Progress message
                    Text(
                      context.tr('library.downloading'),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
                actions: [
                  // Cancel button
                  TextButton.icon(
                    onPressed: () async {
                      // Cancel the download
                      final cancelled = await libraryProvider.cancelDownload(item);
                      if (cancelled && mounted) {
                        // Close the dialog immediately after cancellation
                        Navigator.of(context).pop();

                        // Show cancellation message
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                const Icon(
                                  Icons.info,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(context.tr('library.download_cancelled')),
                              ],
                            ),
                            backgroundColor: Colors.orange,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                            ),
                            margin: const EdgeInsets.all(8),
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.cancel, size: 18),
                    label: Text(context.tr('common.cancel')),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ],
              );
            },
          );
        },
      );

      try {
        final success = await libraryProvider.downloadItem(item);

        // Check if the download was cancelled by the user
        final wasCancelled = libraryProvider.error == 'USER_CANCELLED';

        // If the download was cancelled, the dialog is already closed by the cancel button
        // So we just need to clear the error state
        if (wasCancelled) {
          libraryProvider.clearError();
          return; // Exit early, no need to proceed further
        }

        // For successful or failed downloads (not cancellations), close the dialog and show message
        if (mounted) {
          // Close the progress dialog
          Navigator.of(context).pop();

          // Show success or error message
          final errorMessage = libraryProvider.error.isNotEmpty
              ? libraryProvider.error
              : context.tr('library.download_error');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    success ? Icons.check_circle : Icons.error,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      success
                          ? context.tr('library.download_success')
                          : errorMessage,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              backgroundColor: success ? Colors.green : Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              margin: const EdgeInsets.all(8),
              duration: const Duration(seconds: 4),
            ),
          );

          // Clear the error after showing it
          if (!success) {
            libraryProvider.clearError();
          }
        }
      } catch (e) {
        // Check if the download was cancelled by the user
        final wasCancelled = libraryProvider.error == 'USER_CANCELLED';

        // If the download was cancelled, the dialog is already closed by the cancel button
        // So we just need to clear the error state
        if (wasCancelled) {
          libraryProvider.clearError();
          return; // Exit early, no need to proceed further
        }

        // For errors (not cancellations), close the dialog and show error message
        if (mounted) {
          try {
            // Try to close the dialog - wrap in try/catch in case it's already closed
            Navigator.of(context).pop();
          } catch (dialogError) {
            // Dialog might already be closed, ignore the error
          }

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(
                    Icons.error,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      context.tr('library.download_error'),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
              ),
              margin: const EdgeInsets.all(8),
              duration: const Duration(seconds: 4),
            ),
          );
        }
      }
    }
  }

  // Helper method to get content type icon
  IconData _getContentTypeIcon(LibraryItem item) {
    // Check if this is an external link item
    if (item.isExternalLink) {
      return Icons.language;
    } else if (item.isPdf) {
      return Icons.picture_as_pdf;
    } else if (item.isAudio) {
      return Icons.audiotrack;
    } else if (item.isVideo) {
      return Icons.video_library;
    } else {
      return Icons.insert_drive_file;
    }
  }

  // Helper method to get content type color
  Color _getContentTypeColor(LibraryItem item) {
    return Theme.of(context).getContentTypeColor(item.contentType);
  }

  Widget _buildContentTypeIcon(LibraryItem item) {
    final theme = Theme.of(context);
    final iconData = _getContentTypeIcon(item);
    final iconColor = _getContentTypeColor(item);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            theme.colorScheme.surfaceVariant,
            iconColor.withOpacity(0.15),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              iconData,
              size: 28,
              color: iconColor,
            ),
            const SizedBox(height: 4),
            Text(
              item.fileExtension.toUpperCase(),
              style: theme.boldSmallText.copyWith(
                color: iconColor,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build the search bar widget
  Widget _buildSearchBar(LibraryProvider libraryProvider) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
          // Search field
          Expanded(
            child: SizedBox(
              height: 40,
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: context.tr('library.search_hint'),
                  hintStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    fontSize: 14,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    size: 18,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            size: 16,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                          ),
                          onPressed: () {
                            _searchController.clear();
                            libraryProvider.search('');
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          splashRadius: 16,
                        )
                      : null,
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1.5,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
                ),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 14,
                ),
                textInputAction: TextInputAction.search,
                onChanged: (value) {
                  // Search on each keystroke
                  libraryProvider.search(value);
                },
                onSubmitted: (value) {
                  // Also search when the user presses enter
                  libraryProvider.search(value);
                  // Unfocus the text field
                  FocusScope.of(context).unfocus();
                },
              ),
            ),
          ),

          // Filter button
          IconButton(
            icon: Icon(
              _showFilters ? Icons.filter_alt_off : Icons.filter_alt,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
              size: 22,
            ),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
              // Unfocus the text field when opening/closing filters
              FocusScope.of(context).unfocus();
            },
            tooltip: context.tr('library.filter'),
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            visualDensity: VisualDensity.compact,
          ),
          // Small spacing between buttons
          const SizedBox(width: 4),

          // View toggle button
          IconButton(
            icon: Icon(
              libraryProvider.isGridView ? Icons.view_list : Icons.grid_view,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
              size: 22,
            ),
            onPressed: () {
              libraryProvider.toggleViewMode();
              // Unfocus the text field when toggling view
              FocusScope.of(context).unfocus();
            },
            tooltip: libraryProvider.isGridView
                ? context.tr('library.list_view')
                : context.tr('library.grid_view'),
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            visualDensity: VisualDensity.compact,
          ),

        ],
      ),
    );
  }


}
