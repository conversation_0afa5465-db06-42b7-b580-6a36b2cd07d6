import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../widgets/universal_library_card.dart';
import '../../localization/app_localizations_extension.dart';
import '../../utils/responsive_utils.dart';
import '../../theme/app_theme.dart';
import '../../providers/library_provider.dart';
import '../../services/content_handler_service.dart';

class LibraryItemsPage extends StatefulWidget {
  const LibraryItemsPage({super.key});

  @override
  State<LibraryItemsPage> createState() => _LibraryItemsPageState();
}

class _LibraryItemsPageState extends State<LibraryItemsPage>
    with AutomaticKeepAliveClientMixin {
  // Filter visibility
  bool _showFilters = false;
  // Search controller
  final TextEditingController _searchController = TextEditingController();

  // Override wantKeepAlive to keep this page alive when not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Initialize the library provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<LibraryProvider>(context, listen: false).init();
    });

    // Add listener to search controller to update the UI when text changes
    _searchController.addListener(() {
      setState(() {
        // This will rebuild the UI to show/hide the clear button
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Handle search input changes
  void _onSearchChanged(String value, LibraryProvider libraryProvider) {
    libraryProvider.search(value);
  }

  // Clear search
  void _clearSearch(LibraryProvider libraryProvider) {
    _searchController.clear();
    libraryProvider.search('');
    FocusScope.of(context).unfocus();
  }

  // Toggle filter visibility
  void _toggleFilters() {
    setState(() {
      _showFilters = !_showFilters;
    });
    // Unfocus the text field when toggling filters
    FocusScope.of(context).unfocus();
  }

  // Handle item opening
  Future<void> _openLibraryItem(
      dynamic item, LibraryProvider libraryProvider) async {
    // Use ContentHandlerService to open the library item (handles PDF and other types)
    if (mounted) {
      final success = await ContentHandlerService.openLibraryItem(
        context,
        item,
        showErrorSnackbar: true,
      );

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('library.item_not_available')),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // Build search bar
  Widget _buildSearchBar(LibraryProvider libraryProvider) {
    return Container(

      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      //margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: _searchController,
              onChanged: (value) => _onSearchChanged(value, libraryProvider),
              decoration: InputDecoration(
                hintText: context.tr('library.search_hint'),
                prefixIcon: Icon(
                  Icons.search,
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.7),
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.6),
                    ),
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),

          // Clear search button (only show when there's text)
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: Icon(
                Icons.clear,
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.7),
                size: 20,
              ),
              onPressed: () => _clearSearch(libraryProvider),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(),
              visualDensity: VisualDensity.compact,
            ),

          // Filter toggle button
          IconButton(
            icon: Icon(
              _showFilters ? Icons.filter_alt_off : Icons.filter_alt,
              color:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
              size: 22,
            ),
            onPressed: _toggleFilters,
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            visualDensity: VisualDensity.compact,
          ),

          // Small spacing between buttons
          // const SizedBox(width: 4),

          // Grid/List view toggle
          // IconButton(
          //   icon: Icon(
          //     libraryProvider.isGridView ? Icons.view_list : Icons.grid_view,
          //     color:
          //         Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
          //     size: 22,
          //   ),
          //   onPressed: () {
          //     libraryProvider.toggleViewMode();
          //     // Unfocus the text field when toggling view
          //     FocusScope.of(context).unfocus();
          //   },
          //   tooltip: libraryProvider.isGridView
          //       ? context.tr('library.list_view')
          //       : context.tr('library.grid_view'),
          //   padding: const EdgeInsets.all(8),
          //   constraints: const BoxConstraints(),
          //   visualDensity: VisualDensity.compact,
          // ),
        ],
      ),
    );
  }

  // Build filter options
  Widget _buildFilterOptions(LibraryProvider libraryProvider) {
    final contentTypes = libraryProvider.availableContentTypes;
    final categories = libraryProvider.availableCategories;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .surfaceContainerHighest
            .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content Type Filter
          if (contentTypes.length > 1)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.filter_alt,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      context.tr('library.content_type'),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: [
                    ChoiceChip(
                      label: Text(
                        context.tr('library.all'),
                        style: Theme.of(context)
                            .textTheme
                            .labelMedium
                            ?.copyWith(
                              fontWeight:
                                  libraryProvider.selectedContentType == 'all'
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              color:
                                  libraryProvider.selectedContentType == 'all'
                                      ? Theme.of(context).colorScheme.onPrimary
                                      : Theme.of(context).colorScheme.onSurface,
                            ),
                      ),
                      selected: libraryProvider.selectedContentType == 'all',
                      onSelected: (selected) {
                        if (selected) {
                          libraryProvider.filterByContentType('all');
                        }
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary,
                      showCheckmark: false,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                      elevation:
                          libraryProvider.selectedContentType == 'all' ? 2 : 0,
                    ),
                    ...contentTypes.where((type) => type != 'all').map((type) =>
                        ChoiceChip(
                          label: Text(
                            type.toUpperCase(),
                            style: Theme.of(context)
                                .textTheme
                                .labelMedium
                                ?.copyWith(
                                  fontWeight:
                                      libraryProvider.selectedContentType ==
                                              type
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                  color: libraryProvider.selectedContentType ==
                                          type
                                      ? Theme.of(context).colorScheme.onPrimary
                                      : Theme.of(context).colorScheme.onSurface,
                                ),
                          ),
                          selected: libraryProvider.selectedContentType == type,
                          onSelected: (selected) {
                            libraryProvider
                                .filterByContentType(selected ? type : 'all');
                          },
                          backgroundColor:
                              Theme.of(context).colorScheme.surface,
                          selectedColor: Theme.of(context).colorScheme.primary,
                          showCheckmark: false,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 0),
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                          visualDensity: VisualDensity.compact,
                          elevation: libraryProvider.selectedContentType == type
                              ? 2
                              : 0,
                        )),
                  ],
                ),
              ],
            ),

          const SizedBox(height: 12),

          // Category Filter
          if (categories.length > 1)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.category,
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      context.tr('library.category'),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: [
                    FilterChip(
                      label: Text(
                        context.tr('library.all'),
                        style: Theme.of(context)
                            .textTheme
                            .labelMedium
                            ?.copyWith(
                              fontWeight:
                                  libraryProvider.selectedCategory == 'all'
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              color: libraryProvider.selectedCategory == 'all'
                                  ? Theme.of(context).colorScheme.onPrimary
                                  : Theme.of(context).colorScheme.onSurface,
                            ),
                      ),
                      selected: libraryProvider.selectedCategory == 'all',
                      onSelected: (selected) {
                        if (selected) {
                          libraryProvider.filterByCategory('all');
                        }
                      },
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      selectedColor: Theme.of(context).colorScheme.primary,
                      showCheckmark: false,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 0),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                      elevation:
                          libraryProvider.selectedCategory == 'all' ? 2 : 0,
                    ),
                    ...categories
                        .where((category) => category != 'all')
                        .map((category) => FilterChip(
                              label: Text(
                                category,
                                style: Theme.of(context)
                                    .textTheme
                                    .labelMedium
                                    ?.copyWith(
                                      fontWeight:
                                          libraryProvider.selectedCategory ==
                                                  category
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                      color: libraryProvider.selectedCategory ==
                                              category
                                          ? Theme.of(context)
                                              .colorScheme
                                              .onPrimary
                                          : Theme.of(context)
                                              .colorScheme
                                              .onSurface,
                                    ),
                              ),
                              selected:
                                  libraryProvider.selectedCategory == category,
                              onSelected: (selected) {
                                libraryProvider.filterByCategory(
                                    selected ? category : 'all');
                              },
                              backgroundColor:
                                  Theme.of(context).colorScheme.surface,
                              selectedColor:
                                  Theme.of(context).colorScheme.primary,
                              showCheckmark: false,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 0),
                              materialTapTargetSize:
                                  MaterialTapTargetSize.shrinkWrap,
                              visualDensity: VisualDensity.compact,
                              elevation:
                                  libraryProvider.selectedCategory == category
                                      ? 2
                                      : 0,
                            )),
                  ],
                ),
              ],
            ),
          const SizedBox(height: 8),

          // Reset filters button
          if (libraryProvider.selectedContentType != 'all' ||
              libraryProvider.selectedCategory != 'all')
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Center(
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .errorContainer
                        .withValues(alpha: 0.2),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: TextButton(
                    onPressed: () {
                      libraryProvider.resetFilters();
                      _searchController.clear();
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.error,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 2),
                      minimumSize: const Size(0, 24),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.refresh, size: 14),
                        const SizedBox(width: 4),
                        Text(
                          context.tr('courses.reset_filters'),
                          style:
                              Theme.of(context).textTheme.labelMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to maintain the AutomaticKeepAliveClientMixin
    super.build(context);

    return Scaffold(
        appBar: AppBar(
          title: Text(context.tr('library.items')),
          toolbarHeight: 0,
          elevation: 0,
        ),
        body: SafeArea(
            child: Column(children: [
          // Search bar
          Consumer<LibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildSearchBar(libraryProvider);
            },
          ),
          // Filter options
          if (_showFilters)
            Consumer<LibraryProvider>(
              builder: (context, libraryProvider, child) {
                return _buildFilterOptions(libraryProvider);
              },
            ),
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 0.0),
                child: Consumer<LibraryProvider>(
                  builder: (context, libraryProvider, child) {
                    if (libraryProvider.isLoading &&
                        libraryProvider.items.isEmpty) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    if (libraryProvider.error.isNotEmpty &&
                        libraryProvider.items.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                libraryProvider.error,
                                style: const TextStyle(color: Colors.red),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 20),
                              ElevatedButton(
                                onPressed: () => libraryProvider
                                    .fetchLibraryItems(forceRefresh: true),
                                child: Text(context.tr('errors.try_again')),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    if (libraryProvider.items.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Text(context.tr('library.no_items')),
                        ),
                      );
                    }

                    if (libraryProvider.filteredItems.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Text(context.tr('library.no_matching_items')),
                        ),
                      );
                    } else {
                      return libraryProvider.isGridView
                          // Grid View
                          ? GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount:
                                    ResponsiveUtils.getResponsiveGridCount(
                                        context: context),
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                childAspectRatio: AppTheme.cardAspectRatio,
                              ),
                              itemCount: libraryProvider.filteredItems.length,
                              itemBuilder: (context, index) {
                                final item =
                                    libraryProvider.filteredItems[index];
                                return UniversalLibraryCard(
                                  item: item,
                                  isHorizontal: false,
                                  onDownload: (item) => _openLibraryItem(item,
                                      libraryProvider), // No download, just open
                                  onOpen: (item) =>
                                      _openLibraryItem(item, libraryProvider),
                                );
                              },
                            )
                          // List View
                          : ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: libraryProvider.filteredItems.length,
                              separatorBuilder: (context, index) =>
                                  const SizedBox(height: 8),
                              itemBuilder: (context, index) {
                                final item =
                                    libraryProvider.filteredItems[index];
                                return UniversalLibraryCard(
                                  item: item,
                                  isHorizontal: true,
                                  onDownload: (item) => _openLibraryItem(item,
                                      libraryProvider), // No download, just open
                                  onOpen: (item) =>
                                      _openLibraryItem(item, libraryProvider),
                                );
                              },
                            );
                    }
                  },
                ),
              ),
            ),
          )
        ])));
  }
}
