import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/career_guidance_provider.dart';
import 'career_quiz_screen.dart';
import 'career_results_screen.dart';

class CareerPage extends StatefulWidget {
  const CareerPage({Key? key}) : super(key: key);

  @override
  State<CareerPage> createState() => _CareerPageState();
}

class _CareerPageState extends State<CareerPage> {
  @override
  void initState() {
    super.initState();
    _loadExistingResults();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh results when returning to this page
    _loadExistingResults();
  }

  void _loadExistingResults() {
    // Load existing results when the page loads (if not already loaded)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final provider =
          Provider.of<CareerGuidanceProvider>(context, listen: false);
      // Only load if we don't have results and we're not already loading
      if (provider.result == null && !provider.isLoading) {
        provider.loadExistingResults();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<CareerGuidanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // Show error message if there's an error loading results
          if (provider.error.isNotEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Theme.of(context).colorScheme.error,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading results',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Theme.of(context).colorScheme.error,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      provider.error,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () {
                        provider.clearError();
                        _loadExistingResults();
                      },
                      child: const Text('Try Again'),
                    ),
                  ],
                ),
              ),
            );
          }

          // Main content based on state - show appropriate screen directly
          if (provider.result != null) {
            return CareerResultsScreen(result: provider.result!);
          } else {
            return const CareerQuizScreen();
          }
        },
      ),
    );
  }
}
