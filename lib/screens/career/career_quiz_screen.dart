import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/career_guidance_model.dart';
import '../../models/question_model.dart';
import '../../providers/career_guidance_provider.dart';
import '../../theme/app_theme.dart';
import '../../localization/app_localizations_extension.dart';
import 'career_results_screen.dart';

class CareerQuizScreen extends StatefulWidget {
  const CareerQuizScreen({Key? key}) : super(key: key);

  @override
  State<CareerQuizScreen> createState() => _CareerQuizScreenState();
}

class _CareerQuizScreenState extends State<CareerQuizScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _questionController;
  late Animation<double> _progressAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _questionController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
        parent: _questionController, curve: Curves.easeOutCubic));

    _questionController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text(context.tr('career.quiz_title')),
        toolbarHeight: 0,
        elevation: 0,
      ),
      body: Consumer<CareerGuidanceProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.error.isNotEmpty) {
            return _buildErrorState(context, provider);
          }

          if (!provider.quizStarted) {
            return _buildStartScreen(context, provider);
          }

          if (provider.quizCompleted && provider.result != null) {
            return CareerResultsScreen(result: provider.result!);
          }

          return _buildQuizContent(context, provider);
        },
      ),
    );
  }

  Widget _buildStartScreen(
      BuildContext context, CareerGuidanceProvider provider) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Quiz illustration
            Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Image.asset("assets/images/career_guidance.png")
            ),
            const SizedBox(height: 32),

            // Title
            Text(
              context.tr('career.welcome_title'),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              context.tr('career.welcome_description'),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color:
                Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Quiz info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color:
                Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.timer,
                          color: Theme.of(context).colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('career.estimated_time'),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.quiz,
                          color: Theme.of(context).colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        context.tr('career.total_questions'),
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Start button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: provider.isLoading ? null : () => _startQuiz(provider),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: provider.isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(
                  context.tr('career.start_quiz'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizContent(
      BuildContext context, CareerGuidanceProvider provider) {
    final currentQuestion = provider.currentQuestion;
    if (currentQuestion == null) return const SizedBox();

    // Update progress animation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _progressController.animateTo(provider.progressPercentage / 100);
    });

    return Column(
      children: [
        // Progress bar
        _buildProgressBar(context, provider),

        // Question content
        Expanded(
          child: SlideTransition(
            position: _slideAnimation,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Question counter
                  Text(
                    '${provider.currentQuestionIndex + 1} of ${provider.totalQuestions}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.6),
                        ),
                  ),
                  const SizedBox(height: 32),

                  // Question text
                  Expanded(
                    child: Center(
                      child: Text(
                        currentQuestion.statement,
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  height: 1.4,
                                ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),

                  // Answer options
                  _buildAnswerOptions(context, provider, currentQuestion),

                  const SizedBox(height: 24),

                  // Navigation buttons
                  _buildNavigationButtons(context, provider),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(
      BuildContext context, CareerGuidanceProvider provider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                context.tr('career.progress'),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              Text(
                '${provider.progressPercentage.toInt()}%',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _progressAnimation.value,
                backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
                minHeight: 6,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOptions(BuildContext context,
      CareerGuidanceProvider provider, CareerQuestion question) {
    return Column(
      children: [
        Text(
          context.tr('career.how_much_agree'),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: LikertScale.values.map((scale) {
            final isSelected = question.answer == scale;
            return GestureDetector(
              onTap: () => _answerQuestion(provider, scale),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.surfaceVariant,
                  shape: BoxShape.circle,
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Theme.of(context)
                                .colorScheme
                                .primary
                                .withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: Text(
                    scale.emoji,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              context.tr('career.strongly_disagree'),
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
            Text(
              context.tr('career.strongly_agree'),
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNavigationButtons(
      BuildContext context, CareerGuidanceProvider provider) {
    return Row(
      children: [
        if (provider.hasPreviousQuestion)
          Expanded(
            child: OutlinedButton(
              onPressed: () => provider.goToPreviousQuestion(),
              child: Text(context.tr('career.previous')),
            ),
          ),
        if (provider.hasPreviousQuestion) const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: provider.currentQuestion?.answer != null
                ? () => _proceedToNext(provider)
                : null,
            child: Text(provider.hasNextQuestion
                ? context.tr('career.next')
                : context.tr('career.finish')),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(
      BuildContext context, CareerGuidanceProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              context.tr('career.something_went_wrong'),
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              provider.error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => provider.clearError(),
              child: Text(context.tr('career.try_again')),
            ),
          ],
        ),
      ),
    );
  }

  void _startQuiz(CareerGuidanceProvider provider) async {
    final success = await provider.startQuiz();
    if (success) {
      _questionController.reset();
      _questionController.forward();
    }
  }

  void _answerQuestion(
      CareerGuidanceProvider provider, LikertScale answer) async {
    await provider.answerQuestion(answer);

    // Animate to next question
    if (provider.hasNextQuestion || !provider.quizCompleted) {
      _questionController.reset();
      _questionController.forward();
    }
  }

  void _proceedToNext(CareerGuidanceProvider provider) async {
    if (provider.currentQuestion?.answer != null) {
      final answer = provider.currentQuestion!.answer!;
      _answerQuestion(provider, answer);
    }
  }

  void _showExitConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.tr('career.exit_quiz')),
        content: Text(context.tr('career.exit_confirmation')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.tr('career.cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Close quiz screen
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(context.tr('career.exit')),
          ),
        ],
      ),
    );
  }
}
