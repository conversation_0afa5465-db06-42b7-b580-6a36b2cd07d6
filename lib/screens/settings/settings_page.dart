import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/logger_service.dart';
import '../../services/secure_storage_service.dart';
import '../../services/language_service.dart';
import '../../screens/auth/login_screen.dart';
import '../../theme/app_theme.dart';
import '../../config/app_config.dart';
import '../../localization/app_localizations.dart';
import '../../localization/app_localizations_extension.dart';
import '../../providers/app_settings_provider.dart';
import '../../providers/user_provider.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _isLoading = false;

  // Get the app settings provider
  AppSettingsProvider get _appSettings =>
      Provider.of<AppSettingsProvider>(context, listen: false);

  Future<void> _logout() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Use the user provider's logout method to ensure all data is cleared
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.logout();

      if (!mounted) return;

      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
    } catch (e) {
      LoggerService.error('Logout error', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(context.tr('errors.something_went_wrong'))),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('settings.settings')),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              children: [
                const SizedBox(height: 20),

                // Account settings section
                _buildSectionHeader(context.tr('settings.account')),

                ListTile(
                  leading: const Icon(Icons.lock),
                  title: Text(context.tr('auth.password')),
                  subtitle: Text(context.tr('auth.password_requirements')),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // Navigate to change password page
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(context.tr('auth.password') +
                              ' ' +
                              context.tr('common.coming_soon'))),
                    );
                  },
                ),

                const Divider(),

                // Appearance settings
                _buildSectionHeader(context.tr('settings.appearance')),

                Consumer<AppSettingsProvider>(
                  builder: (context, appSettings, child) {
                    return SwitchListTile(
                      title: Text(context.tr('settings.dark_mode')),
                      subtitle: Text(appSettings.isDarkMode
                          ? context.tr('settings.dark_mode_on')
                          : context.tr('settings.dark_mode_off')),
                      secondary: Icon(appSettings.isDarkMode
                          ? Icons.dark_mode
                          : Icons.light_mode),
                      value: appSettings.isDarkMode,
                      onChanged: (value) async {
                        final success = await appSettings.toggleThemeMode();
                        if (success && mounted) {
                          _showSnackBar(context.tr('settings.theme_changed'));
                        }
                      },
                    );
                  },
                ),

                // Language selection
                ListTile(
                  leading: const Icon(Icons.language),
                  title: Text(context.tr('settings.language')),
                  subtitle: Text(AppConfig.getLocaleDisplayName(
                      AppLocalizations.of(context).locale.languageCode)),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () => _showLanguageSelectionDialog(),
                ),

                const Divider(),

                // About section
                _buildSectionHeader(context.tr('settings.about')),

                ListTile(
                    leading: const Icon(Icons.info),
                    title: Text(context.tr('settings.about')),
                    subtitle: Text(context.tr('settings.version') + ' 1.0.0')),

                ListTile(
                  leading: const Icon(Icons.help),
                  title: Text(context.tr('settings.help')),
                  subtitle: Text(context.tr('settings.help')),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // Navigate to help page
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(context.tr('settings.help') +
                              ' ' +
                              context.tr('common.coming_soon'))),
                    );
                  },
                ),

                const Divider(),

                // Logout section
                _buildSectionHeader(context.tr('settings.session')),

                ListTile(
                  leading: Icon(Icons.logout, color: AppTheme.errorColor),
                  title: Text(context.tr('auth.logout'),
                      style: TextStyle(color: AppTheme.errorColor)),
                  onTap: () {
                    // Show logout confirmation
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: Text(context.tr('auth.logout')),
                        content: Text(context.tr('auth.logout_confirmation')),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(context.tr('common.cancel')),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _logout();
                            },
                            child: Text(
                              context.tr('auth.logout'),
                              style: TextStyle(color: AppTheme.errorColor),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const SizedBox(height: 50),
              ],
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(
        left: AppTheme.spacingMedium,
        top: AppTheme.spacingMedium,
        bottom: AppTheme.spacingSmall,
      ),
      child: Text(
        title,
        style: TextStyle(
          fontSize: AppTheme.fontSizeSmall,
          fontWeight: FontWeight.bold,
          color: AppTheme.textSecondaryColor,
        ),
      ),
    );
  }

  // Show language selection dialog
  void _showLanguageSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(context.tr('settings.select_language')),
          content: SizedBox(
            width: double.maxFinite,
            child: FutureBuilder<String>(
              future: LanguageService.getCurrentLanguageCode(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                final currentLanguage = snapshot.data!;
                final availableLanguages =
                    LanguageService.getAvailableLanguages();

                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: availableLanguages.length,
                  itemBuilder: (context, index) {
                    final language = availableLanguages[index];
                    final languageCode = language['languageCode']!;
                    final languageName = language['name']!;

                    return RadioListTile<String>(
                      title: Text(languageName),
                      value: languageCode,
                      groupValue: currentLanguage,
                      onChanged: (value) async {
                        if (value != null) {
                          // Close the dialog
                          Navigator.pop(context);

                          // Change the language
                          await _changeLanguage(value);
                        }
                      },
                    );
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(context.tr('common.cancel')),
            ),
          ],
        );
      },
    );
  }

  // Change the app language
  Future<void> _changeLanguage(String languageCode) async {
    try {
      // Set the new locale using the app settings provider
      final success = await _appSettings.setLocale(languageCode);

      if (success) {
        // Show success message
        _showSnackBar(context.tr('settings.language_changed'));
      } else {
        _showSnackBar(context.tr('errors.something_went_wrong'));
      }
    } catch (e) {
      LoggerService.error('Error changing language', e);
      _showSnackBar(context.tr('errors.something_went_wrong'));
    }
  }

  // Show a snackbar message
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
