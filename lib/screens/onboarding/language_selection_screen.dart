import 'package:ZABAI/localization/app_localizations_extension.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/app_config.dart';
import '../../providers/app_settings_provider.dart';
import '../../services/first_launch_service.dart';
import '../../services/logger_service.dart';
import '../auth/login_screen.dart';
import '../home/<USER>';
import '../../services/secure_storage_service.dart';

class LanguageSelectionScreen extends StatefulWidget {
  const LanguageSelectionScreen({super.key});

  @override
  State<LanguageSelectionScreen> createState() =>
      _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  String? _selectedLanguage;
  bool _isLoading = false;
  AppSettingsProvider get _appSettings => Provider.of<AppSettingsProvider>(context, listen: false);
  @override
  void initState() {
    super.initState();
    // Set default language to Myanmar (Burmese)
    _selectedLanguage = 'en';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                // App Logo
                Container(
                  width: 120,
                  height: 120,
                  child: Image.asset(
                      'assets/images/zabai-logo.png',
                      fit: BoxFit.contain,
                      width: 100,
                      height: 100,
                    ),
                ),
                const SizedBox(height: 12),

                // Welcome Title
                Text(
                  context.tr('welcome.title'),
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Subtitle
                Text(
                  context.tr('welcome.subtitle'),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // Language Options
                ...AppConfig.getEnabledLocalesWithNames().map((locale) {
                  final languageCode = locale['languageCode']!;
                  final languageName = locale['name']!;
                  final nativeName = _getNativeName(languageCode);

                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: _buildLanguageCard(
                      languageCode: languageCode,
                      displayName: languageName,
                      nativeName: nativeName,
                      isSelected: _selectedLanguage == languageCode,
                      onTap: () {
                        setState(() {
                          _selectedLanguage = languageCode;
                          _appSettings.setLocale(languageCode);
                        });
                      },
                    ),
                  );
                }),

                // Continue Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _onContinuePressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            context.tr('common.next'),
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageCard({
    required String languageCode,
    required String displayName,
    required String nativeName,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
              : Colors.white,
          border: Border.all(
            color:
                isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: Row(
          children: [
            // Language Flag
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isSelected
                    ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                    : Colors.grey[50],
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.3)
                      : Colors.grey[200]!,
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  _getLanguageFlag(languageCode),
                  style: const TextStyle(fontSize: 28),
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Language Name
            Expanded(
              child: Text(
                nativeName,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.grey[800],
                ),
              ),
            ),

            // Selection Indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  String _getNativeName(String languageCode) {
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'my':
        return 'မြန်မာ';
      case 'bn':
        return 'বাংলা';
      default:
        return languageCode;
    }
  }

  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'en':
        return '🇬🇧'; // US flag for English
      case 'my':
        return '🇲🇲'; // Myanmar flag
      case 'bn':
        return '🇧🇩'; // Bangladesh flag for Bengali
      default:
        return '🌐'; // Globe emoji as fallback
    }
  }

  Future<void> _onContinuePressed() async {
    if (_selectedLanguage == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Set the selected language
      final appSettings =
          Provider.of<AppSettingsProvider>(context, listen: false);
      await appSettings.setLocale(_selectedLanguage!);

      // Mark app as launched
      await FirstLaunchService.markAsLaunched();

      // Check if user is logged in
      final hasToken = await SecureStorageService.hasToken();

      // Navigate to appropriate screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                hasToken ? const Dashboard() : const LoginPage(),
          ),
        );
      }
    } catch (e) {
      LoggerService.error('Error setting language and continuing', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Something went wrong. Please try again.'),
            duration: Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
