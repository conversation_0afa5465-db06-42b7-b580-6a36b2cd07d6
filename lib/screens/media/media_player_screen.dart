import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import '../../providers/user_provider.dart';
import '../../services/logger_service.dart';
import '../../services/module_progress_service.dart';

class MediaPlayerScreen extends StatefulWidget {
  final String mediaUrl;
  final String title;
  final int? moduleId; // Optional module ID for tracking completion

  const MediaPlayerScreen({
    super.key,
    required this.mediaUrl,
    required this.title,
    this.moduleId,
  });

  @override
  State<MediaPlayerScreen> createState() => _MediaPlayerScreenState();
}

class _MediaPlayerScreenState extends State<MediaPlayerScreen> {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;
  bool _hasMarkedAsAccessed = false;
  bool _hasMarkedAsCompleted = false;
  Duration _totalDuration = Duration.zero;
  Duration _currentPosition = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _markAsAccessed();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  /// Initialize the video player
  Future<void> _initializePlayer() async {
    try {
      LoggerService.info('Initializing media player for: ${widget.mediaUrl}');
      
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.mediaUrl));
      
      await _controller!.initialize();
      
      _controller!.addListener(_onPlayerUpdate);
      
      setState(() {
        _isLoading = false;
        _totalDuration = _controller!.value.duration;
      });
      
      LoggerService.info('Media player initialized successfully');
    } catch (e) {
      LoggerService.error('Error initializing media player', e);
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to load media: ${e.toString()}';
      });
    }
  }

  /// Handle player updates
  void _onPlayerUpdate() {
    if (_controller != null && mounted) {
      final position = _controller!.value.position;
      final duration = _controller!.value.duration;
      
      setState(() {
        _currentPosition = position;
        _totalDuration = duration;
      });
      
      // Check for completion (watched at least 80% of the video)
      if (duration.inMilliseconds > 0) {
        final watchedPercentage = position.inMilliseconds / duration.inMilliseconds;
        if (watchedPercentage >= 0.8 && !_hasMarkedAsCompleted) {
          _markAsCompleted();
        }
      }
    }
  }

  /// Mark module as accessed when media is opened
  Future<void> _markAsAccessed() async {
    if (widget.moduleId != null && !_hasMarkedAsAccessed) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;
        
        if (user != null) {
          await ModuleProgressService.markAsAccessed(
            userId: user.id,
            moduleId: widget.moduleId!,
            contentType: 'Audio / Video',
          );
          _hasMarkedAsAccessed = true;
          LoggerService.debug('Marked media module ${widget.moduleId} as accessed');
        }
      } catch (e) {
        LoggerService.error('Error marking media module as accessed', e);
      }
    }
  }

  /// Mark module as completed
  Future<void> _markAsCompleted() async {
    if (widget.moduleId != null && !_hasMarkedAsCompleted) {
      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final user = userProvider.user;
        
        if (user != null) {
          await ModuleProgressService.markAsCompleted(
            userId: user.id,
            moduleId: widget.moduleId!,
            contentType: 'Audio / Video',
            progressData: {
              'total_duration_ms': _totalDuration.inMilliseconds,
              'watched_duration_ms': _currentPosition.inMilliseconds,
              'completion_percentage': _currentPosition.inMilliseconds / _totalDuration.inMilliseconds,
            },
          );
          _hasMarkedAsCompleted = true;
          LoggerService.debug('Marked media module ${widget.moduleId} as completed');
          
          // Show completion message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Module completed!'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      } catch (e) {
        LoggerService.error('Error marking media module as completed', e);
      }
    }
  }

  /// Format duration for display
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading media...'),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'Failed to load media',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _hasError = false;
                  _errorMessage = null;
                });
                _initializePlayer();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_controller == null || !_controller!.value.isInitialized) {
      return const Center(
        child: Text('Media player not initialized'),
      );
    }

    return Column(
      children: [
        // Video player
        Expanded(
          child: Center(
            child: AspectRatio(
              aspectRatio: _controller!.value.aspectRatio,
              child: VideoPlayer(_controller!),
            ),
          ),
        ),
        
        // Controls
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Progress bar
              VideoProgressIndicator(
                _controller!,
                allowScrubbing: true,
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
              
              // Time display
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(_formatDuration(_currentPosition)),
                  Text(_formatDuration(_totalDuration)),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Play/Pause button
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    onPressed: () {
                      setState(() {
                        if (_controller!.value.isPlaying) {
                          _controller!.pause();
                        } else {
                          _controller!.play();
                        }
                      });
                    },
                    icon: Icon(
                      _controller!.value.isPlaying
                          ? Icons.pause_circle_filled
                          : Icons.play_circle_filled,
                      size: 64,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
