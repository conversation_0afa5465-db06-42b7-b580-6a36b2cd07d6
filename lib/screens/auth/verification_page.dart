import 'package:flutter/material.dart';
import '../../services/api_service.dart';
import '../../screens/auth/login_screen.dart';
import '../../services/logger_service.dart';

class VerificationPage extends StatefulWidget {
  const VerificationPage({Key? key}) : super(key: key);

  @override
  _VerificationPageState createState() => _VerificationPageState();
}

class _VerificationPageState extends State<VerificationPage> {
  final TextEditingController _otpController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  // Show a snackbar message
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Verify OTP
  Future<void> _verifyOtp() async {
    // Validate OTP
    if (_otpController.text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter the OTP';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      LoggerService.info('Verifying OTP');
      final response = await ApiService.verifyOtp(_otpController.text);

      // Check if widget is still mounted
      if (!mounted) return;

      if (response['status'] == 'success') {
        LoggerService.info('OTP verification successful');
        _showSnackBar('Verification successful!');

        // Navigate to login page on success
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(builder: (context) => const LoginPage()),
              (route) => false, // Remove all previous routes
            );
          }
        });
      } else {
        LoggerService.warning('OTP verification failed: ${response['message']}');
        setState(() {
          _errorMessage = response['message'] ?? 'Invalid OTP';
        });
      }
    } catch (e) {
      LoggerService.error('OTP verification error', e);
      if (mounted) {
        setState(() {
          _errorMessage = 'Verification failed: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('OTP Verification'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 20),

            // Verification icon
            Icon(
              Icons.verified_user,
              size: 80,
              color: theme.primaryColor,
            ),

            const SizedBox(height: 30),

            // Instructions
            const Text(
              'Verification Code',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 15),

            Text(
              'We have sent a verification code to your email/phone. Please enter it below.',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // OTP input field
            TextField(
              controller: _otpController,
              decoration: InputDecoration(
                labelText: 'Enter OTP',
                hintText: '123456',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                prefixIcon: const Icon(Icons.lock_outline),
                errorText: _errorMessage,
              ),
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 20,
                letterSpacing: 8,
                fontWeight: FontWeight.bold,
              ),
              maxLength: 6,
              enabled: !_isLoading,
            ),

            const SizedBox(height: 30),

            // Verify button
            SizedBox(
              width: double.infinity,
              height: 56, // Increased height for better text display
              child: ElevatedButton(
                onPressed: _isLoading ? null : _verifyOtp,
                // Using theme's button style with minor customizations
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  textStyle: const TextStyle(fontSize: 18),
                ),
                child: _isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).colorScheme.onPrimary,
                          ),
                          strokeWidth: 2.5,
                        ),
                      )
                    : const Text('Verify'),
              ),
            ),

            const SizedBox(height: 20)
          ],
        ),
      ),
    );
  }
}
