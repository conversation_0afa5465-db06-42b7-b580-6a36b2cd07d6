import 'package:flutter/material.dart';
import '../../screens/auth/verification_page.dart';
import '../../services/api_service.dart';
import '../../screens/auth/login_screen.dart';
import '../../services/logger_service.dart';
import '../../theme/app_theme.dart';
import '../../localization/app_localizations_extension.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({Key? key}) : super(key: key);

  @override
  _SignUpPageState createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();

  final _firstnameController = TextEditingController();
  final _lastnameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  // Field-specific error messages
  String? _usernameError;
  String? _emailError;
  String? _phoneError;

  @override
  void dispose() {
    _firstnameController.dispose();
    _lastnameController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  // Show a snackbar message
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: AppTheme.fontSizeRegular,
            color: Theme.of(context).colorScheme.onError,
          ),
        ),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : AppTheme.successColor,
        duration: const Duration(seconds: 5), // Increased to 5 seconds
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
      ),
    );
  }

  // Enhanced password validation
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return context.tr('auth.required_field');
    }

    if (value.length < 8) {
      return context.tr('auth.password_requirements');
    }

    // Check for password complexity
    bool hasUppercase = value.contains(RegExp(r'[A-Z]'));
    bool hasDigits = value.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    if (!hasUppercase || !hasDigits || !hasSpecialCharacters) {
      return context.tr('auth.password_requirements');
    }

    return null;
  }

  Future<void> _register() async {
    // Clear previous field-specific errors
    setState(() {
      _usernameError = null;
      _emailError = null;
      _phoneError = null;
    });

    // Validate form
    if (_formKey.currentState!.validate()) {
      // Set loading state
      setState(() {
        _isLoading = true;
      });

      final firstname = _firstnameController.text;
      final lastname = _lastnameController.text;
      final username = _usernameController.text;
      final password = _passwordController.text;
      final confirmPassword = _confirmPasswordController.text;
      final email = _emailController.text;
      final phone = _phoneController.text;

      try {
        LoggerService.info('Registering user: $username');

        // Call API for registration
        final response = await ApiService.register(
          firstname,
          lastname,
          username,
          password,
          confirmPassword,
          email,
          phone
        );

        // Check if widget is still mounted before using context
        if (!mounted) return;

        if (response['error'] != null) {
          LoggerService.warning('Registration failed: ${response['error']}');

          // Check for field-specific errors
          if (response.containsKey('field_errors') && response['field_errors'] is Map) {
            final fieldErrors = response['field_errors'] as Map;
            bool hasDisplayedError = false;

            // Handle email errors
            if (fieldErrors.containsKey('email') && fieldErrors['email'] is List && fieldErrors['email'].isNotEmpty) {
              setState(() {
                _emailError = fieldErrors['email'][0];
              });
              hasDisplayedError = true;
            }

            // Handle username errors
            if (fieldErrors.containsKey('username') && fieldErrors['username'] is List && fieldErrors['username'].isNotEmpty) {
              setState(() {
                _usernameError = fieldErrors['username'][0];
              });
              hasDisplayedError = true;
            }

            // Handle phone errors
            if (fieldErrors.containsKey('phone') && fieldErrors['phone'] is List && fieldErrors['phone'].isNotEmpty) {
              setState(() {
                _phoneError = fieldErrors['phone'][0];
              });
              hasDisplayedError = true;
            }

            // If we have general errors or no specific field errors were displayed, show a snackbar
            if (fieldErrors.containsKey('__all__') && fieldErrors['__all__'] is List && fieldErrors['__all__'].isNotEmpty) {
              _showSnackBar(fieldErrors['__all__'][0], isError: true);
              hasDisplayedError = true;
            }

            // If no specific errors were displayed, fall back to the general error message
            if (!hasDisplayedError) {
              _showSnackBar(response['error'], isError: true);
            }
          } else {
            // Fallback to the old behavior for backward compatibility
            final errorMsg = response['error'];

            if (errorMsg.toLowerCase().contains('username')) {
              setState(() {
                _usernameError = errorMsg;
              });
            } else if (errorMsg.toLowerCase().contains('email')) {
              setState(() {
                _emailError = errorMsg;
              });
            } else if (errorMsg.toLowerCase().contains('phone')) {
              setState(() {
                _phoneError = errorMsg;
              });
            } else {
              // Generic error
              _showSnackBar(errorMsg, isError: true);
            }
          }
        } else {
          // Success
          LoggerService.info('User registered successfully');
          _showSnackBar(context.tr('auth.register_success'));

          // Navigate to verification page
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const VerificationPage()),
          );
        }
      } catch (e) {
        LoggerService.error('Registration error', e);

        if (mounted) {
          _showSnackBar(context.tr('auth.register_failed'), isError: true);
        }
      } finally {
        // Reset loading state if widget is still mounted
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.tr('auth.create_account')),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppTheme.spacingLarge),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // First Name Field
                TextFormField(
                  controller: _firstnameController,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.first_name'),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr('auth.required_field');
                    }
                    return null;
                  },
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingMedium),

                // Last Name Field
                TextFormField(
                  controller: _lastnameController,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.last_name'),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr('auth.required_field');
                    }
                    return null;
                  },
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingMedium),

                // Username Field with error handling
                TextFormField(
                  controller: _usernameController,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.username'),
                    prefixIcon: const Icon(Icons.account_circle),
                    errorText: _usernameError,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr('auth.required_field');
                    }
                    if (value.length < 4) {
                      return context.tr('auth.username') + " " + context.tr('auth.password_requirements');
                    }
                    return null;
                  },
                  onChanged: (value) {
                    if (_usernameError != null) {
                      setState(() {
                        _usernameError = null;
                      });
                    }
                  },
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingMedium),

                // Password Field with enhanced validation
                TextFormField(
                  controller: _passwordController,
                  obscureText: !_isPasswordVisible,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.password'),
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                    ),
                    helperText: context.tr('auth.password_requirements'),
                    helperMaxLines: 4,
                    errorMaxLines: 4,   // <-- add this line
                  ),
                  validator: _validatePassword,
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingMedium),

                // Confirm Password Field
                TextFormField(
                  controller: _confirmPasswordController,
                  obscureText: !_isConfirmPasswordVisible,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.confirm_password'),
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                        });
                      },
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr('auth.required_field');
                    } else if (value != _passwordController.text) {
                      return context.tr('auth.password_mismatch');
                    }
                    return null;
                  },
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingLarge),

                // Email Field with error handling
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.email'),
                    prefixIcon: const Icon(Icons.email),
                    errorText: _emailError,
                  ),
                  validator: (value) {
                    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
                    if (value == null || value.isEmpty) {
                      if (_phoneController.text.isEmpty) {
                        return context.tr('auth.email') + " " + context.tr('auth.required_field');
                      }
                      return null;
                    }
                    if (!emailRegex.hasMatch(value)) {
                      return context.tr('auth.invalid_email');
                    }
                    return null;
                  },
                  onChanged: (value) {
                    if (_emailError != null) {
                      setState(() {
                        _emailError = null;
                      });
                    }
                  },
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingMedium),

                // Phone Number Field with error handling
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: context.tr('auth.phone'),
                    prefixIcon: const Icon(Icons.phone),
                    errorText: _phoneError,
                  ),
                  validator: (value) {
                    final phoneRegex = RegExp(r'^\+?[0-9]{10,15}$');
                    if (value == null || value.isEmpty) {
                      if (_emailController.text.isEmpty) {
                        return context.tr('auth.phone') + " " + context.tr('auth.required_field');
                      }
                      return null;
                    }
                    if (!phoneRegex.hasMatch(value)) {
                      return context.tr('auth.invalid_phone');
                    }
                    return null;
                  },
                  onChanged: (value) {
                    if (_phoneError != null) {
                      setState(() {
                        _phoneError = null;
                      });
                    }
                  },
                  enabled: !_isLoading,
                ),
                SizedBox(height: AppTheme.spacingLarge),

                // Sign-Up Button with loading indicator
                SizedBox(
                  width: double.infinity,
                  height: 56, // Consistent height for button
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _register,
                    // Using theme's button style - no need to specify style
                    child: _isLoading
                        ? SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).colorScheme.onPrimary,
                              ),
                              strokeWidth: 2.5,
                            ),
                          )
                        : Text(
                            context.tr('auth.sign_up'),
                            style: TextStyle(
                              fontSize: AppTheme.fontSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                SizedBox(height: AppTheme.spacingLarge),

                // Already Have an Account
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      context.tr('auth.already_have_account'),
                      style: TextStyle(
                        fontSize: AppTheme.fontSizeRegular,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    TextButton(
                      onPressed: _isLoading ? null : () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LoginPage(),
                          ),
                        );
                      },
                      child: Text(
                        context.tr('auth.login'),
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeRegular,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
