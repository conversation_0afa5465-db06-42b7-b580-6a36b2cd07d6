import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../screens/auth/register_screen.dart';
import '../../services/logger_service.dart';
import '../../theme/app_theme.dart';
import '../../localization/app_localizations_extension.dart';
import '../../services/api_service.dart';
import '../../screens/home/<USER>';
import '../../providers/user_provider.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  LoginPageState createState() => LoginPageState();
}

class LoginPageState extends State<LoginPage> {
  bool _isPasswordVisible = false;
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _usernameError;
  String? _passwordError;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Validate username in real-time
  String? _validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return context.tr('auth.required_field');
    }
    return null;
  }

  // Basic password validation - only checks if empty
  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return context.tr('auth.required_field');
    }
    return null;
  }

  // Show a snackbar message
  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: AppTheme.fontSizeRegular,
            color: Theme.of(context).colorScheme.onError,
          ),
        ),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : AppTheme.successColor,
        duration: const Duration(seconds: 5), // Increased to 5 seconds
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
      ),
    );
  }

  // Login function
  Future<void> _login() async {
    // Clear previous errors
    setState(() {
      _usernameError = null;
      _passwordError = null;
    });

    // Basic validation - just check if fields are empty
    final username = _usernameController.text;
    final password = _passwordController.text;

    if (username.isEmpty) {
      setState(() {
        _usernameError = context.tr('auth.required_field');
      });
      return;
    }

    if (password.isEmpty) {
      setState(() {
        _passwordError = context.tr('auth.required_field');
      });
      return;
    }

    // Set loading state
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the user provider
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Attempt login using the provider
      final success = await userProvider.login(username, password);

      // Check if widget is still mounted before using context
      if (!mounted) return;

      if (!success) {
        // Handle login failure
        final errorMsg = userProvider.error;
        LoggerService.warning('Login failed: $errorMsg');

        // Check for field-specific errors from the API
        final fieldErrors = userProvider.fieldErrors;
        bool hasDisplayedError = false;

        if (fieldErrors != null && fieldErrors.isNotEmpty) {
          LoggerService.debug('Processing field errors: $fieldErrors');

          // Handle the case where errors is a List (from login response)
          if (fieldErrors.containsKey('message') &&
              fieldErrors['message'] is List &&
              fieldErrors['message'].isNotEmpty) {
            // Show the error message as a snackbar
            _showSnackBar(fieldErrors['message'][0], isError: true);
            hasDisplayedError = true;
          } else {
            // Handle username errors
            if (fieldErrors.containsKey('username') &&
                fieldErrors['username'] is List &&
                fieldErrors['username'].isNotEmpty) {
              setState(() {
                _usernameError = fieldErrors['username'][0];
              });
              hasDisplayedError = true;
            }

            // Handle password errors
            if (fieldErrors.containsKey('password') &&
                fieldErrors['password'] is List &&
                fieldErrors['password'].isNotEmpty) {
              setState(() {
                _passwordError = fieldErrors['password'][0];
              });
              hasDisplayedError = true;
            }

            // Handle non-field errors
            if (fieldErrors.containsKey('__all__') &&
                fieldErrors['__all__'] is List &&
                fieldErrors['__all__'].isNotEmpty) {
              _showSnackBar(fieldErrors['__all__'][0], isError: true);
              hasDisplayedError = true;
            }
          }
        }

        // If no specific field errors were displayed, use the general error handling
        if (!hasDisplayedError) {
          // Provide user-friendly error messages based on the general error
          if (errorMsg.toLowerCase().contains('status: 400') ||
              errorMsg.toLowerCase().contains('request failed')) {
            // This is likely a generic error message from the API response parsing
            // Show a more user-friendly message instead
            _showSnackBar("Username or password is incorrect", isError: true);
          } else if (errorMsg.toLowerCase().contains('invalid') ||
              errorMsg.toLowerCase().contains('credentials') ||
              errorMsg.toLowerCase().contains('incorrect')) {
            // Authentication failure - wrong username or password
            _showSnackBar(errorMsg, isError: true);
          } else if (errorMsg.toLowerCase().contains('username')) {
            // Username-specific error
            setState(() {
              _usernameError = errorMsg;
            });
          } else if (errorMsg.toLowerCase().contains('password')) {
            // Password-specific error
            setState(() {
              _passwordError = errorMsg;
            });
          } else if (errorMsg.toLowerCase().contains('connect') ||
              errorMsg.toLowerCase().contains('network') ||
              errorMsg.toLowerCase().contains('timeout')) {
            // Connection issues
            _showSnackBar(context.tr('errors.connection_error'), isError: true);
          } else {
            // Generic error with a more user-friendly message
            _showSnackBar(errorMsg, isError: true);
            // Log the actual error for debugging
            LoggerService.debug('Showing generic error: $errorMsg');
          }
        }
      } else {
        LoggerService.info('Login successful');
        _showSnackBar(context.tr('auth.login_success'));

        // Navigate to Home Page
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const Dashboard()),
        );
      }
    } catch (e) {
      // Handle any other exceptions
      LoggerService.error('Error during login', e);

      if (mounted) {
        _showSnackBar(context.tr('errors.try_again'), isError: true);
      }
    } finally {
      // Reset loading state if widget is still mounted
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: AppTheme.spacingLarge),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo
                  Image.asset(
                    'assets/images/zabai-logo.png',
                    width: 200,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: AppTheme.spacingXXLarge),

                  // Username Field with validation
                  TextFormField(
                    controller: _usernameController,
                    decoration: InputDecoration(
                      labelText: context.tr('auth.username'),
                      prefixIcon: const Icon(Icons.person),
                      errorText: _usernameError,
                      // Using theme's input decoration - no need to specify border
                    ),
                    style: const TextStyle(fontSize: AppTheme.fontSizeMedium),
                    validator: _validateUsername,
                    onChanged: (value) {
                      // Clear error when user types
                      if (_usernameError != null) {
                        setState(() {
                          _usernameError = null;
                        });
                      }
                    },
                    enabled: !_isLoading,
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),

                  // Password Field with show/hide but no real-time validation
                  TextFormField(
                    controller: _passwordController,
                    obscureText: !_isPasswordVisible,
                    decoration: InputDecoration(
                      labelText: context.tr('auth.password'),
                      prefixIcon: const Icon(Icons.lock),
                      errorText: _passwordError,
                      // Using theme's input decoration - no need to specify border
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isPasswordVisible
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            _isPasswordVisible = !_isPasswordVisible;
                          });
                        },
                      ),
                    ),
                    style: const TextStyle(fontSize: AppTheme.fontSizeMedium),
                    // Only validate on form submission
                    validator: _validatePassword,
                    onChanged: (value) {
                      // Only clear server-side errors when user types
                      if (_passwordError != null) {
                        setState(() {
                          _passwordError = null;
                        });
                      }
                    },
                    enabled: !_isLoading,
                  ),

                  // Forgot Password link
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: _isLoading
                          ? null
                          : () {
                              // Navigate to forgot password page
                              _showSnackBar(context.tr('auth.forgot_password') +
                                  ' ' +
                                  context.tr('common.coming_soon'));
                            },
                      child: Text(
                        context.tr('auth.forgot_password'),
                        style: const TextStyle(
                          fontSize: AppTheme.fontSizeRegular,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),

                  // Login Button with loading indicator
                  SizedBox(
                    width: double.infinity,
                    height: 56, // Consistent height for button
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _login,
                      // Using theme's button style - no need to specify style
                      child: _isLoading
                          ? SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).colorScheme.onPrimary,
                                ),
                                strokeWidth: 2.5,
                              ),
                            )
                          : Text(
                              context.tr('auth.sign_in'),
                              style: TextStyle(
                                fontSize: AppTheme.fontSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: AppTheme.spacingLarge),

                  // Sign Up link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        context.tr('auth.dont_have_account'),
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeRegular,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.7),
                        ),
                      ),
                      TextButton(
                        onPressed: _isLoading
                            ? null
                            : () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const SignUpPage(),
                                  ),
                                );
                              },
                        child: Text(
                          context.tr('auth.sign_up'),
                          style: TextStyle(
                            fontSize: AppTheme.fontSizeRegular,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
