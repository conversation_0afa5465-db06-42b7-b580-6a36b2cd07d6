import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/material.dart';
import 'package:archive/archive.dart';
import 'package:dio/dio.dart';
import 'dart:io';

import '../../config/app_config.dart';
import '../../localization/app_localizations.dart';
import '../../repositories/course_repository.dart';
import '../../services/logger_service.dart';

class DownloadPage extends StatefulWidget {
  final String courseSlug;
  final String moduleSlug;
  final String downloadLink;

  const DownloadPage({
    Key? key,
    required this.courseSlug,
    required this.moduleSlug,
    required this.downloadLink,
  }) : super(key: key);

  @override
  _DownloadPageState createState() => _DownloadPageState();
}

class _DownloadPageState extends State<DownloadPage> {
  final Dio _dio = Dio();
  final CourseRepository _courseRepository = CourseRepository();
  bool _cancelRequested = false;
  double _downloadProgress = 0.0;
  double _extractProgress = 0.0;
  String _downloadedFilePath = '';

  @override
  void initState() {
    super.initState();
    _downloadAndExtractZip();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final double imageSize = screenSize.height * 0.4;
    
    return Scaffold(
      // appBar: AppBar(
      //   title: Text('Downloading ${widget.moduleSlug}'),
      // ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Show image with circular progress indicator during download
            if (_downloadProgress < 1.0) ...[
              Stack(
                alignment: Alignment.center,
                children: [
                  // CircularProgressIndicator behind the image
                  Container(
                    width: imageSize + 20, // Add some padding for the progress indicator
                    height: imageSize + 20,
                    child: CircularProgressIndicator(
                      value: _downloadProgress,
                      strokeWidth: 8.0, // Customize the stroke width
                      color: Colors.redAccent,
                    ),
                  ),
                  // Circular frame for the image
                  ClipOval(
                    child: Image.asset(
                      'assets/images/downloadAnim.gif', // Path to your animated GIF or image
                      width: imageSize, // Set the width of the image
                      height: imageSize, // Set the height of the image
                      fit: BoxFit.cover, // Cover the circular area
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 36),
              Text(
              // 'Downloading... ${(_downloadProgress * 100).toStringAsFixed(0)}%'
                  AppLocalizations.of(context).translate('download.waiting')
              ),
            ],
            // Show completion message after download
            // if (_downloadProgress >= 1.0) ...[
            //   const Text(
            //     'Download Completed!',
            //     style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            //   ),
            //   const SizedBox(height: 36),
            // ],
            // Show image with circular progress indicator during extraction
            if (_extractProgress < 1.0 && _downloadProgress >= 1.0) ...[
              Stack(
                alignment: Alignment.center,
                children: [
                  // CircularProgressIndicator behind the image
                  Container(
                    width: imageSize + 20, // Add some padding for the progress indicator
                    height: imageSize + 20,
                    child: CircularProgressIndicator(
                      value: _extractProgress,
                      strokeWidth: 8.0, // Customize the stroke width
                      color: Colors.blueAccent,
                    ),
                  ),
                  // Circular frame for the image
                  ClipOval(
                    child: Image.asset(
                      'assets/images/extractAnim.gif', // Path to your animated GIF or image
                      width: imageSize, // Set the width of the image
                      height: imageSize, // Set the height of the image
                      fit: BoxFit.cover, // Cover the circular area
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 36),
              Text(
                // 'Extracting... ${(_extractProgress * 100).toStringAsFixed(0)}%',
                  AppLocalizations.of(context).translate('download.extracting')
              ),
            ],
            const SizedBox(height: 36),
            ElevatedButton.icon(
              onPressed: _downloadProgress >= 1.0 && _extractProgress >= 1.0
                  ? () {
                // Go back to the previous screen
                Navigator.pop(context, true);
              }
                  : () {
                // Cancel the download and extraction process
                //_cancelDownloadAndExtraction();
                _deleteFolder();
              },
              icon: Icon(Icons.cancel),
              label: Text(_downloadProgress >= 1.0 && _extractProgress >= 1.0 ? 'Close' : 'Cancel'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                    horizontal: 30, vertical: 15),
                foregroundColor: Theme
                    .of(context)
                    .colorScheme
                    .onError, // Use onPrimary color for text
                backgroundColor: Theme
                    .of(context)
                    .colorScheme.error, // Use primary color from theme
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadAndExtractZip() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final coursePath = '/my_courses/${widget.courseSlug}/${widget.moduleSlug}';
    final zipUrl = AppConfig.apiBaseUrl + widget.downloadLink;

    // Check if the asset exists before attempting to copy
    final assetPath = 'assets/courses/${widget.moduleSlug}.zip';

    Completer<bool> assetLoadingCompleter = Completer<bool>();

    try {
      await rootBundle.load(assetPath).then((_) {
        assetLoadingCompleter.complete(true);
      }).catchError((error) {
        assetLoadingCompleter.complete(false);
      });
      print("assetPath : $assetPath");
    } catch (e) {
      assetLoadingCompleter.complete(false);
    }

    bool assetLoaded = await assetLoadingCompleter.future;
    print("assetLoaded : $assetLoaded");

    if (assetLoaded) {
      print("DOWNLOADING OFFLINE !!!!");
      setState(() {
        _downloadProgress = 1.0; // Set download progress to completed
      });
      try {
        // Create the target directory for extraction
        final targetDir = Directory('${appDocDir.path}$coursePath');
        if (!targetDir.existsSync()) {
          targetDir.createSync(recursive: true);
        }
        // Copying asset to the target directory
        final assetPath = 'assets/courses/${widget.moduleSlug}.zip';
        final tempDir = await getTemporaryDirectory();
        final tempZipFile = File('${tempDir.path}/${widget.moduleSlug}.zip');

        ByteData data = await rootBundle.load(assetPath);
        List<int> assetBytes = data.buffer.asUint8List();

        await tempZipFile.writeAsBytes(assetBytes);
        final destinationZipFile = File('${targetDir.path}/${widget.moduleSlug}.zip');
        await tempZipFile.copy(destinationZipFile.path);

        // Extract the copied zip file
        final bytes = await destinationZipFile.readAsBytes();
        final archive = ZipDecoder().decodeBytes(bytes);
        final totalFiles = archive.length;
        int extractedFiles = 0;

        for (final file in archive) {
          // Extract the file
          final extractedFile = File('${targetDir.path}/${file.name}');
          if (file.isFile) {
            await extractedFile.create(recursive: true);
            await extractedFile.writeAsBytes(file.content);
          }
          extractedFiles++;

          // Calculate extraction progress
          final progress = (extractedFiles / totalFiles);
          setState(() {
            _extractProgress = progress;
          });
        }

        // Delete the copied zip file
        await destinationZipFile.delete();

        // Update database to mark module as downloaded
        final localPath = '${targetDir.path}';
        await _courseRepository.updateModuleDownloadStatus(
          widget.moduleSlug,
          true,
          localPath: localPath
        );
        LoggerService.info('Updated database: module ${widget.moduleSlug} marked as downloaded');

        // Go back to the previous screen
        Navigator.pop(context, true);
      } catch (e) {
        print('Error during extraction: $e');
        // Handle the error here if needed
      }
    } else {

      _checkInternetAndDownload();
      print("DOWNLOADING ONLINE !!!!");
      print("\n$zipUrl\n");

      try {
        // Download the zip file using Dio
        final response = await _dio.download(
          zipUrl,
          '${appDocDir.path}$coursePath/${widget.moduleSlug}.zip',
          onReceiveProgress: (received, total) {
            if (_cancelRequested) {                                                                                                                                                                                                 
              // Cancel the download by throwing an error
              throw DioError(
                requestOptions: RequestOptions(path: zipUrl),
                error: 'Download canceled by user.',
                type: DioErrorType.cancel,
              );
            } else {
              // Calculate download percentage
              final progress = total != -1 ? (received / total) : 0;
              setState(() {
                _downloadProgress = progress.toDouble();
              });
            }
          },
        );

        _downloadedFilePath = response.data.toString();

        // Set the flag to false after download
        _cancelRequested = false;

        // Start the extraction process after download completes
        final zipFile = File('${appDocDir.path}$coursePath/${widget.moduleSlug}.zip');
        final bytes = await zipFile.readAsBytes();
        final archive = ZipDecoder().decodeBytes(bytes);
        final totalFiles = archive.length;
        int extractedFiles = 0;

        for (final file in archive) {
          // Extract the file
          final extractedFile = File('${appDocDir.path}$coursePath/${file.name}');
          if (file.isFile) {
            await extractedFile.create(recursive: true);
            await extractedFile.writeAsBytes(file.content);
          }
          extractedFiles++;

          // Calculate extraction progress
          final progress = (extractedFiles / totalFiles);
          setState(() {
            _extractProgress = progress;
          });
        }
        // Delete the zip file after extraction
        await zipFile.delete();

        // Set the flag to false after extraction
        _cancelRequested = false;

        // Update database to mark module as downloaded
        final localPath = '${appDocDir.path}$coursePath';
        await _courseRepository.updateModuleDownloadStatus(
          widget.moduleSlug,
          true,
          localPath: localPath
        );
        LoggerService.info('Updated database: module ${widget.moduleSlug} marked as downloaded');

        // Go back to the previous screen
        Navigator.pop(context, true);
      } catch (e) {
        if (_cancelRequested && e is DioError && e.type == DioErrorType.cancel) {
          print('Download and extraction canceled.');
          // Delete the downloaded zip file
          File(_downloadedFilePath).deleteSync();
          // Delete any partially extracted files
          _deleteFolder();
        } else {
          print('Error during download and extraction: $e');
          showErrorDialog(context," Error during download and extraction, \n please contact the administrator !!");
        }
      }
    }
  }

  Future<void> _checkInternetAndDownload() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {

    } else {
      // Not connected to the internet, show a pop-up notification
      showInternetNotConnectedDialog(context);
    }
  }

  Future<void> _deleteFolder() async {
    _cancelRequested = true;

    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      final directory = Directory(
          '${appDocDir.path}/my_courses/${widget.courseSlug}/${widget.moduleSlug}');

      // Check if the folder exists before attempting to delete it
      if (await directory.exists()) {
        // Delete the folder
        await directory.delete(recursive: true);

        // Update database to mark module as not downloaded
        await _courseRepository.updateModuleDownloadStatus(
          widget.moduleSlug,
          false,
          localPath: ''
        );
        LoggerService.info('Updated database: module ${widget.moduleSlug} marked as not downloaded');
        //print('Folder deleted successfully.');
      } else {
        //print('Folder not found.');
      }
    } catch (e) {
      //print('Error while deleting folder: $e');
    }

    // Go back to the previous screen
    Navigator.pop(context, true);
  }
}

void showErrorDialog(BuildContext context, String message) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text("😟 Opps!!"),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text("OK"),
        ),
      ],
    ),
  );
}

void showInternetNotConnectedDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text("No Internet Connection"),
        content: const Text(
            "Please connect to the internet and try again."),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text("OK"),
          ),
        ],
      );
    },
  );
}
