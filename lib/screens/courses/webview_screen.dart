import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../localization/app_localizations_extension.dart';
import '../../services/secure_storage_service.dart';
import '../../services/logger_service.dart';
import 'package:flutter/services.dart';


class WebViewScreen extends StatefulWidget {
  final String url;

  const WebViewScreen({
    super.key,
    required this.url,
  });

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  WebViewController? controller;
  bool isLoading = true;
  bool isInitializing = true;

  @override
  void initState() {
    super.initState();

    // Lock screen to landscape orientation
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    _initializeWebView();
  }

  @override
  void dispose() {
    // Revert orientation back to portrait
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  Future<void> _initializeWebView() async {
    try {
      final token = await SecureStorageService.getToken();

      final webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              if (mounted) {
                setState(() {
                  isLoading = true;
                });
              }
            },
            onPageFinished: (String url) {
              if (mounted) {
                setState(() {
                  isLoading = false;
                });
              }
            },
            onWebResourceError: (WebResourceError error) {
              LoggerService.error('WebView error: ${error.description}', error);
            },
          ),
        );

      if (token != null && token.isNotEmpty) {
        await webViewController.loadRequest(
          Uri.parse(widget.url),
          headers: {
            'Authorization': 'Bearer $token',
          },
        );
        LoggerService.info('WebView loaded with authentication token');
      } else {
        await webViewController.loadRequest(Uri.parse(widget.url));
        LoggerService.warning('WebView loaded without authentication token');
      }

      if (mounted) {
        setState(() {
          controller = webViewController;
          isInitializing = false;
        });
      }
    } catch (e) {
      LoggerService.error('Error initializing WebView', e);
      if (mounted) {
        setState(() {
          isInitializing = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(context.tr('errors.something_went_wrong')),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Show a loading indicator while initializing
          if (isInitializing)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Initializing content...'),
                ],
              ),
            )
          // Show the WebView only when controller is initialized
          else if (controller != null)
            WebViewWidget(controller: controller!),
          // Show loading indicator on top of WebView while page is loading
          if (!isInitializing && controller != null && isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
