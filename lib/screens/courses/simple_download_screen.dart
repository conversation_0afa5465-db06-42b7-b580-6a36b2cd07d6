import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

import '../../config/app_config.dart';
import '../../localization/app_localizations.dart';
import '../../repositories/course_repository.dart';
import '../../services/logger_service.dart';

/// Simple download page for non-SCORM content (PDF, audio, video)
/// Downloads files directly without extraction
class SimpleDownloadPage extends StatefulWidget {
  final String courseSlug;
  final String moduleSlug;
  final String downloadLink;
  final String contentType;

  const SimpleDownloadPage({
    Key? key,
    required this.courseSlug,
    required this.moduleSlug,
    required this.downloadLink,
    required this.contentType,
  }) : super(key: key);

  @override
  _SimpleDownloadPageState createState() => _SimpleDownloadPageState();
}

class _SimpleDownloadPageState extends State<SimpleDownloadPage> {
  final Dio _dio = Dio();
  final CourseRepository _courseRepository = CourseRepository();
  bool _cancelRequested = false;
  double _downloadProgress = 0.0;
  String _downloadedFilePath = '';
  bool _isCompleted = false;

  @override
  void initState() {
    super.initState();
    _downloadFile();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final double imageSize = screenSize.height * 0.4;

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).translate('download.title')),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Download progress section
            Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // Circular progress indicator
                    SizedBox(
                      width: imageSize,
                      height: imageSize,
                      child: CircularProgressIndicator(
                        value: _downloadProgress,
                        strokeWidth: 8.0,
                        backgroundColor: Colors.grey[300],
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Colors.redAccent,
                        ),
                      ),
                    ),
                    // Circular frame for the image
                    ClipOval(
                      child: Image.asset(
                        'assets/images/downloadAnim.gif',
                        width: imageSize,
                        height: imageSize,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 36),
                Text(
                  _isCompleted
                      ? AppLocalizations.of(context).translate('download.completed')
                      : '${AppLocalizations.of(context).translate('download.downloading')} ${(_downloadProgress * 100).toStringAsFixed(0)}%',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'Content Type: ${widget.contentType}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 36),
            ElevatedButton.icon(
              onPressed: _isCompleted
                  ? () {
                      // Go back to the previous screen
                      Navigator.pop(context, true);
                    }
                  : () {
                      // Cancel the download process
                      _cancelDownload();
                    },
              icon: Icon(_isCompleted ? Icons.check : Icons.cancel),
              label: Text(_isCompleted ? 'Close' : 'Cancel'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                foregroundColor: _isCompleted
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onError,
                backgroundColor: _isCompleted
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.error,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadFile() async {
    try {
      // Check internet connectivity
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        _showErrorDialog('No internet connection available');
        return;
      }

      // Get cross-platform compatible storage directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final coursePath = '/my_courses/${widget.courseSlug}/${widget.moduleSlug}';
      final targetDir = Directory('${appDocDir.path}$coursePath');

      // Create target directory if it doesn't exist
      if (!await targetDir.exists()) {
        await targetDir.create(recursive: true);
      }

      // Get file extension from download link
      final uri = Uri.parse(widget.downloadLink);
      final fileName = uri.pathSegments.last;
      final fileExtension = fileName.split('.').last;
      
      // Create file name with proper extension
      final downloadFileName = '${widget.moduleSlug}.$fileExtension';
      final filePath = '${targetDir.path}/$downloadFileName';

      final downloadUrl = AppConfig.apiBaseUrl + widget.downloadLink;
      LoggerService.info('Starting simple download: $downloadUrl');

      // Download the file
      final response = await _dio.download(
        downloadUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (_cancelRequested) {
            throw DioException(
              requestOptions: RequestOptions(path: downloadUrl),
              error: 'Download canceled by user.',
              type: DioExceptionType.cancel,
            );
          } else {
            final progress = total != -1 ? (received / total) : 0;
            setState(() {
              _downloadProgress = progress.toDouble();
            });
          }
        },
      );

      _downloadedFilePath = filePath;

      // Mark as completed
      setState(() {
        _isCompleted = true;
        _downloadProgress = 1.0;
      });

      // Update database to mark module as downloaded
      await _courseRepository.updateModuleDownloadStatus(
        widget.moduleSlug,
        true,
        localPath: targetDir.path,
      );
      
      LoggerService.info('Simple download completed: ${widget.moduleSlug}');

      // Auto-close after a short delay
      await Future.delayed(const Duration(seconds: 1));
      if (mounted && _isCompleted) {
        Navigator.pop(context, true);
      }
    } catch (e) {
      LoggerService.error('Error during simple download', e);
      
      if (_cancelRequested && e is DioException && e.type == DioExceptionType.cancel) {
        LoggerService.info('Download canceled by user');
        _deleteDownloadedFile();
      } else {
        _showErrorDialog('Download failed: ${e.toString()}');
      }
    }
  }

  void _cancelDownload() {
    setState(() {
      _cancelRequested = true;
    });
    _deleteDownloadedFile();
    Navigator.pop(context, false);
  }

  Future<void> _deleteDownloadedFile() async {
    try {
      if (_downloadedFilePath.isNotEmpty) {
        final file = File(_downloadedFilePath);
        if (await file.exists()) {
          await file.delete();
        }
      }

      // Update database to mark module as not downloaded
      await _courseRepository.updateModuleDownloadStatus(
        widget.moduleSlug,
        false,
        localPath: '',
      );
      
      LoggerService.info('Cleaned up canceled download: ${widget.moduleSlug}');
    } catch (e) {
      LoggerService.error('Error cleaning up download', e);
    }
  }

  void _showErrorDialog(String message) {
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Download Error'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close dialog
                Navigator.pop(context, false); // Close download page
              },
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
