import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../screens/courses/course_detail_page.dart';
import '../../widgets/universal_course_card.dart';
import '../../localization/app_localizations_extension.dart';
import '../../utils/responsive_utils.dart';
import '../../theme/app_theme.dart';
import '../../providers/course_provider.dart';

class CoursesPage extends StatefulWidget {
  @override
  _CoursesPageState createState() => _CoursesPageState();
}

class _CoursesPageState extends State<CoursesPage>
    with AutomaticKeepAliveClientMixin {
  // Filter visibility
  bool _showFilters = false;
  // Search controller
  final TextEditingController _searchController = TextEditingController();

  // Override wantKeepAlive to keep this page alive when not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Initialize the course provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CourseProvider>(context, listen: false).init();
    });

    // Add listener to search controller to update the UI when text changes
    _searchController.addListener(() {
      setState(() {
        // This will rebuild the UI to show/hide the clear button
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Clear search
  void _clearSearch(CourseProvider courseProvider) {
    _searchController.clear();
    courseProvider.search('');
    FocusScope.of(context).unfocus();
  }

  // Build the search bar widget with filter and toggle icons
  Widget _buildSearchBar(CourseProvider courseProvider) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                // Search on each keystroke
                courseProvider.search(value);
              },
              onSubmitted: (value) {
                // Also search when the user presses enter
                courseProvider.search(value);
                // Unfocus the text field
                FocusScope.of(context).unfocus();
              },
              decoration: InputDecoration(
                hintText: context.tr('library.search_hint'),
                prefixIcon: Icon(
                  Icons.search,
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.7),
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.6),
                    ),
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),

          // Clear search button (only show when there's text)
          if (_searchController.text.isNotEmpty)
            IconButton(
              icon: Icon(
                Icons.clear,
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.7),
                size: 20,
              ),
              onPressed: () => _clearSearch(courseProvider),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(),
              visualDensity: VisualDensity.compact,
            ),

          IconButton(
            icon: Icon(
              _showFilters ? Icons.filter_alt_off : Icons.filter_alt,
              color:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
              size: 22,
            ),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
              // Unfocus the text field when opening/closing filters
              FocusScope.of(context).unfocus();
            },
            padding: const EdgeInsets.all(8),
            constraints: const BoxConstraints(),
            visualDensity: VisualDensity.compact,
          ),

          // Small spacing between buttons
          // const SizedBox(width: 4),

          // // Grid/List view toggle
          // IconButton(
          //   icon: Icon(
          //     courseProvider.isGridView ? Icons.view_list : Icons.grid_view,
          //     color: Theme.of(context).colorScheme.primary.withOpacity(0.7),
          //     size: 22,
          //   ),
          //   onPressed: () {
          //     courseProvider.toggleViewMode();
          //     // Unfocus the text field when toggling view
          //     FocusScope.of(context).unfocus();
          //   },
          //   tooltip: courseProvider.isGridView
          //       ? context.tr('courses.list_view')
          //       : context.tr('courses.grid_view'),
          //   padding: const EdgeInsets.all(8),
          //   constraints: const BoxConstraints(),
          //   visualDensity: VisualDensity.compact,
          // ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to maintain the AutomaticKeepAliveClientMixin
    super.build(context);

    return RefreshIndicator(
        onRefresh: () async {
      await Provider.of<CourseProvider>(context, listen: false).fetchCourses(forceRefresh: true);
    },
    child: Scaffold(
        appBar: AppBar(
          title: Text(context.tr('courses.courses')),
          toolbarHeight: 0,
          elevation: 0,
        ),
        body: SafeArea(
            child: Column(children: [
          // Search bar
          Consumer<CourseProvider>(
            builder: (context, courseProvider, child) {
              return _buildSearchBar(courseProvider);
            },
          ),
          if (_showFilters)
            Consumer<CourseProvider>(
              builder: (context, courseProvider, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.only(bottom: 16.0),
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .surfaceContainerHighest
                        .withValues(alpha: 0.1),
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.03),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Language filter
                      if (courseProvider.availableLanguages.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.language,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  context.tr('courses.language'),
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            SizedBox(
                              height: 32,
                              child: ListView(
                                scrollDirection: Axis.horizontal,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(right: 6.0),
                                    child: ChoiceChip(
                                      label: Text(
                                        context.tr('courses.all'),
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelMedium
                                            ?.copyWith(
                                              fontWeight: courseProvider
                                                          .selectedLanguage ==
                                                      'all'
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                              color: courseProvider
                                                          .selectedLanguage ==
                                                      'all'
                                                  ? Theme.of(context)
                                                      .colorScheme
                                                      .onPrimary
                                                  : Theme.of(context)
                                                      .colorScheme
                                                      .onSurface,
                                            ),
                                      ),
                                      selected:
                                          courseProvider.selectedLanguage ==
                                              'all',
                                      onSelected: (selected) {
                                        if (selected) {
                                          courseProvider.applyFilters(
                                              language: 'all');
                                        }
                                      },
                                      backgroundColor:
                                          Theme.of(context).colorScheme.surface,
                                      selectedColor:
                                          Theme.of(context).colorScheme.primary,
                                      showCheckmark: false,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 0),
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      elevation:
                                          courseProvider.selectedLanguage ==
                                                  'all'
                                              ? 2
                                              : 0,
                                    ),
                                  ),
                                  ...courseProvider.availableLanguages
                                      .map((language) => Padding(
                                            padding: const EdgeInsets.only(
                                                right: 6.0),
                                            child: ChoiceChip(
                                              label: Text(
                                                language,
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .labelMedium
                                                    ?.copyWith(
                                                      fontWeight: courseProvider
                                                                  .selectedLanguage ==
                                                              language
                                                          ? FontWeight.bold
                                                          : FontWeight.normal,
                                                      color: courseProvider
                                                                  .selectedLanguage ==
                                                              language
                                                          ? Theme.of(context)
                                                              .colorScheme
                                                              .onPrimary
                                                          : Theme.of(context)
                                                              .colorScheme
                                                              .onSurface,
                                                    ),
                                              ),
                                              selected: courseProvider
                                                      .selectedLanguage ==
                                                  language,
                                              onSelected: (selected) {
                                                courseProvider.applyFilters(
                                                    language: selected
                                                        ? language
                                                        : 'all');
                                              },
                                              backgroundColor: Theme.of(context)
                                                  .colorScheme
                                                  .surface,
                                              selectedColor: Theme.of(context)
                                                  .colorScheme
                                                  .primary,
                                              showCheckmark: false,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 8,
                                                      vertical: 0),
                                              materialTapTargetSize:
                                                  MaterialTapTargetSize
                                                      .shrinkWrap,
                                              elevation: courseProvider
                                                          .selectedLanguage ==
                                                      language
                                                  ? 2
                                                  : 0,
                                            ),
                                          ))
                                ],
                              ),
                            ),
                          ],
                        ),

                      const SizedBox(height: 12),

                      // Tags filter
                      if (courseProvider.availableTags.isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.tag,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  context.tr('courses.tags'),
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleSmall
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Wrap(
                              spacing: 6,
                              runSpacing: 6,
                              children: [
                                FilterChip(
                                  label: Text(
                                    context.tr('courses.all'),
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelMedium
                                        ?.copyWith(
                                          fontWeight:
                                              courseProvider.selectedTag ==
                                                      'all'
                                                  ? FontWeight.bold
                                                  : FontWeight.normal,
                                          color: courseProvider.selectedTag ==
                                                  'all'
                                              ? Theme.of(context)
                                                  .colorScheme
                                                  .onPrimary
                                              : Theme.of(context)
                                                  .colorScheme
                                                  .onSurface,
                                        ),
                                  ),
                                  selected: courseProvider.selectedTag == 'all',
                                  onSelected: (selected) {
                                    if (selected) {
                                      courseProvider.applyFilters(tag: 'all');
                                    }
                                  },
                                  backgroundColor:
                                      Theme.of(context).colorScheme.surface,
                                  selectedColor:
                                      Theme.of(context).colorScheme.primary,
                                  showCheckmark: false,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 6, vertical: 0),
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  visualDensity: VisualDensity.compact,
                                  elevation: courseProvider.selectedTag == 'all'
                                      ? 2
                                      : 0,
                                ),
                                ...courseProvider.availableTags.map((tag) =>
                                    FilterChip(
                                      label: Text(
                                        tag,
                                        style: Theme.of(context)
                                            .textTheme
                                            .labelMedium
                                            ?.copyWith(
                                              fontWeight:
                                                  courseProvider.selectedTag ==
                                                          tag
                                                      ? FontWeight.bold
                                                      : FontWeight.normal,
                                              color:
                                                  courseProvider.selectedTag ==
                                                          tag
                                                      ? Theme.of(context)
                                                          .colorScheme
                                                          .onPrimary
                                                      : Theme.of(context)
                                                          .colorScheme
                                                          .onSurface,
                                            ),
                                      ),
                                      selected:
                                          courseProvider.selectedTag == tag,
                                      onSelected: (selected) {
                                        courseProvider.applyFilters(
                                            tag: selected ? tag : 'all');
                                      },
                                      backgroundColor:
                                          Theme.of(context).colorScheme.surface,
                                      selectedColor:
                                          Theme.of(context).colorScheme.primary,
                                      showCheckmark: false,
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 0),
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      visualDensity: VisualDensity.compact,
                                      elevation:
                                          courseProvider.selectedTag == tag
                                              ? 2
                                              : 0,
                                    ))
                              ],
                            ),
                          ],
                        ),

                      const SizedBox(height: 8),

                      // Reset filters button
                      if (courseProvider.selectedLanguage != 'all' ||
                          courseProvider.selectedTag != 'all')
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Center(
                            child: Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .colorScheme
                                    .errorContainer
                                    .withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(
                                    AppTheme.borderRadiusSmall),
                              ),
                              child: TextButton(
                                onPressed: () {
                                  courseProvider.resetFilters();
                                  _searchController.clear();
                                },
                                style: TextButton.styleFrom(
                                  foregroundColor:
                                      Theme.of(context).colorScheme.error,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 2),
                                  minimumSize: const Size(0, 24),
                                  tapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(Icons.refresh, size: 14),
                                    const SizedBox(width: 4),
                                    Text(
                                      context.tr('courses.reset_filters'),
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              },
            ),
          Expanded(
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10.0, vertical: 0.0),
                child: Consumer<CourseProvider>(
                  builder: (context, courseProvider, child) {
                    if (courseProvider.isLoading) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    } else if (courseProvider.error.isNotEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Text(courseProvider.error),
                        ),
                      );
                    } else if (courseProvider.courses.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child: Text(context.tr('courses.no_courses')),
                        ),
                      );
                    } else if (courseProvider.filteredCourses.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32.0),
                          child:
                              Text(context.tr('courses.no_matching_courses')),
                        ),
                      );
                    } else {
                      return courseProvider.isGridView
                          // Grid View
                          ? GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount:
                                    ResponsiveUtils.getResponsiveGridCount(
                                        context: context),
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                childAspectRatio: AppTheme.cardAspectRatio,
                              ),
                              itemCount: courseProvider.filteredCourses.length,
                              itemBuilder: (context, index) {
                                final course =
                                    courseProvider.filteredCourses[index];
                                return UniversalCourseCard(
                                  course: course,
                                  isHorizontal: false,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            CourseDetailPage(course: course),
                                      ),
                                    );
                                  },
                                );
                              },
                            )
                          // List View
                          : ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: courseProvider.filteredCourses.length,
                              separatorBuilder: (context, index) =>
                                  const SizedBox(height: 8),
                              itemBuilder: (context, index) {
                                final course =
                                    courseProvider.filteredCourses[index];
                                return UniversalCourseCard(
                                  course: course,
                                  isHorizontal: true,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            CourseDetailPage(course: course),
                                      ),
                                    );
                                  },
                                );
                              },
                            );
                    }
                  },
                ),
              ),
            ),
          )
        ])))
    );
  }
}
