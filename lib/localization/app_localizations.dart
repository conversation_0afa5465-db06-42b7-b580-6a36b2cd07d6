import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../config/app_config.dart';
import '../services/logger_service.dart';

class AppLocalizations {
  final Locale locale;
  Map<String, dynamic> _localizedStrings = {};

  AppLocalizations(this.locale);

  // Helper method to keep the code in the widgets concise
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // Static member to have a simple access to the delegate from the MaterialApp
  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  Future<bool> load() async {
    try {
      // Load the language JSON file from the "assets/lang" folder
      String jsonString = await rootBundle.loadString('assets/lang/${locale.languageCode}.json');
      Map<String, dynamic> jsonMap = json.decode(jsonString);

      // Store the entire JSON structure
      _localizedStrings = Map<String, dynamic>.from(jsonMap);

      LoggerService.debug('Loaded language file for locale: ${locale.languageCode}');
      return true;
    } catch (e) {
      LoggerService.error('Failed to load language file for ${locale.languageCode}', e);
      // If there's an error, load English as fallback
      if (locale.languageCode != AppConfig.defaultLocale) {
        try {
          String jsonString = await rootBundle.loadString('assets/lang/${AppConfig.defaultLocale}.json');
          Map<String, dynamic> jsonMap = json.decode(jsonString);

          // Store the entire JSON structure
          _localizedStrings = Map<String, dynamic>.from(jsonMap);

          LoggerService.debug('Loaded fallback strings for locale: ${AppConfig.defaultLocale}');
          return true;
        } catch (fallbackError) {
          LoggerService.error('Failed to load fallback language file', fallbackError);
          return false;
        }
      }
      return false;
    }
  }

  // This method will be called from every widget that needs a localized text
  String translate(String key, {Map<String, String>? args}) {
    // Handle nested keys like 'auth.username'
    List<String> keys = key.split('.');
    dynamic value;

    try {
      // Start with the full JSON map
      Map<String, dynamic> currentMap = json.decode(json.encode(_localizedStrings));

      // Navigate through the nested structure
      for (int i = 0; i < keys.length; i++) {
        if (i == keys.length - 1) {
          // Last key, get the final value
          value = currentMap[keys[i]];
        } else {
          // Navigate to the next level
          currentMap = currentMap[keys[i]];
        }
      }

      // If value is null, return the key itself
      if (value == null) {
        LoggerService.warning('Translation key not found: $key');
        return key;
      }

      // Handle string replacements if args are provided
      String stringValue = value.toString();
      if (args != null) {
        args.forEach((argKey, argValue) {
          stringValue = stringValue.replaceAll('{$argKey}', argValue);
        });
      }

      return stringValue;
    } catch (e) {
      LoggerService.error('Error translating key: $key', e);
      return key;
    }
  }

  // Format a date according to the current locale
  String formatDate(DateTime date, {String? pattern}) {
    try {
      if (pattern != null) {
        return DateFormat(pattern, locale.languageCode).format(date);
      }
      return DateFormat.yMMMd(locale.languageCode).format(date);
    } catch (e) {
      LoggerService.error('Error formatting date', e);
      // Fallback to a simple format
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Format a number according to the current locale
  String formatNumber(num number) {
    try {
      return NumberFormat.decimalPattern(locale.languageCode).format(number);
    } catch (e) {
      LoggerService.error('Error formatting number', e);
      return number.toString();
    }
  }

  // Format currency according to the current locale
  String formatCurrency(num amount, {String? symbol}) {
    try {
      return NumberFormat.currency(
        locale: locale.languageCode,
        symbol: symbol ?? '\$',
      ).format(amount);
    } catch (e) {
      LoggerService.error('Error formatting currency', e);
      return '$symbol${amount.toString()}';
    }
  }
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppConfig.isLocaleEnabled(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    // AppLocalizations class is where the JSON loading actually occurs
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
