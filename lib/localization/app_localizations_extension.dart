import 'package:flutter/material.dart';
import 'app_localizations.dart';

/// Extension on BuildContext to easily access localizations
extension LocalizationExtension on BuildContext {
  /// Get the AppLocalizations instance
  AppLocalizations get l10n => AppLocalizations.of(this);
  
  /// Translate a string key
  String tr(String key, {Map<String, String>? args}) {
    return l10n.translate(key, args: args);
  }
  
  /// Format a date according to the current locale
  String formatDate(DateTime date, {String? pattern}) {
    return l10n.formatDate(date, pattern: pattern);
  }
  
  /// Format a number according to the current locale
  String formatNumber(num number) {
    return l10n.formatNumber(number);
  }
  
  /// Format currency according to the current locale
  String formatCurrency(num amount, {String? symbol}) {
    return l10n.formatCurrency(amount, symbol: symbol);
  }
}
