<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>ZabaiLMSScormPlayer</title>
    <style>
        body {
            margin: 0;
            height: 100vh;
        }
        .scorm-player {
            width: 100%;
            height: 100vh;
            border: none;
        }
    </style>
</head>
<body data-scorm-id="{{SCORM_ID}}" data-resume-point="{{RESUME_POINT}}">

<iframe
        id="scormPlayer"
        class="scorm-player"
        allowfullscreen="true"
        loading="lazy"
        data-src="{{SCORM_DATA_PATH}}"
></iframe>

<script>
    const ScormErrors = {
      '0' : 'No error (0) No error occurred, the previous API call was successful.',
      '101' : 'General Exception (101) No specific error code exists to describe the error. Use LMSGetDiagnostic for more information.',
      '201' : 'Invalid argument error (201) Indicates that an argument represents an invalid data model element or is otherwise incorrect.',
      '202' : 'Element cannot have children (202) Indicates that LMSGetValue was called with a data model element name that ends in “_children” for a data model element that does not support the “_children” suffix.',
      '203' : 'Element not an array. Cannot have count. (203) Indicates that LMSGetValue was called with a data model element name that ends in “_count” for a data model element that does not support the “_count” suffix.',
      '301' : 'Not initialized (301) Indicates that an API call was made before the call to LMSInitialize.',
      '401' : 'Not implemented error (401) The data model element indicated in a call to LMSGetValue or LMSSetValue is valid, but was not implemented by this LMS. SCORM 1.2 defines a set of data model elements as being optional for an LMS to implement.',
      '402' : 'Invalid set value, element is a keyword (402) Indicates that LMSSetValue was called on a data model element that represents a keyword (elements that end in “_children” and “_count”).',
      '403' : 'Element is read only. (403) LMSSetValue was called with a data model element that can only be read.',
      '404' : 'Element is write only (404) LMSGetValue was called on a data model element that can only be written to.',
      '405' : 'Incorrect Data Type (405) LMSSetValue was called with a value that is not consistent with the data format of the supplied data model element.',
    };

    class ScormAdapter {
        constructor() {
            this.scormId = document.body.dataset.scormId || '';
            this.initializeCmi();
        }

        initializeCmi() {
            const resumePointEncoded = document.body.dataset.resumePoint || '';

            if (!resumePointEncoded || resumePointEncoded === '') {
                console.log('SCORM: No resume point data, using defaults');
                return;
            }

            try {
                const decoded = atob(resumePointEncoded);
                console.log('SCORM: Decoded resume point data length:', decoded.length);

                const existingData = JSON.parse(decoded);
                console.log('SCORM: Parsed resume point data keys:', Object.keys(existingData));

                Object.keys(existingData).forEach(key => {
                    if (this.cmi.hasOwnProperty(key)) {
                        this.cmi[key] = existingData[key];
                        if (key === 'cmi.suspend_data') {
                            console.log('SCORM: Set suspend_data from resume point:',
                                      existingData[key].substring(0, 50) + '...');
                        }
                    } else {
                        console.warn('SCORM: Unknown CMI key from resume point:', key);
                    }
                });

                console.log('SCORM: Initialized with decoded progress data');
            } catch (error) {
                console.error('SCORM: Failed to decode resume point data', error);
                console.log('SCORM: Resume point data:', resumePointEncoded.substring(0, 100) + '...');
                // Don't set the encoded data as suspend_data - that's wrong
                // Just use the default empty values
            }
        }


        cmi = {
            'cmi.core._children': ['student_id', 'student_name', 'lesson_location', 'credit', 'lesson_status', 'entry', 'score', 'total_time', 'lesson_mode', 'exit', 'session_time'],
            'cmi.core.student_id': '1',
            'cmi.core.student_name': '',
            'cmi.core.lesson_location': '',
            'cmi.core.credit': 'no-credit',
            'cmi.core.lesson_status': '',
            'cmi.core.entry': '',
            'cmi.core.score_children': ['raw','min','max'],
            'cmi.core.score.raw': '',
            'cmi.core.score.max': '',
            'cmi.core.score.min': '',
            'cmi.core.total_time': '',
            'cmi.core.lesson_mode': 'normal',
            'cmi.core.exit': '',
            'cmi.core.session_time': '',
            'cmi.suspend_data': '',
            'cmi.launch_data': '',
            'cmi.comments': '',
            'cmi.comments_from_lms': '',
            'cmi.student_data._children': ['mastery_score', 'max_time_allowed', 'time_limit_action' ],
        };

        lastError = '0';

        scormbool = (value) => value === true ? 'true': 'false';

        LMSInitialize = (empty) => {
            // First try to get data from localStorage (session data)
            try {
                var cmi_str = window.localStorage.getItem(this.scormId);
                if (cmi_str && cmi_str !== 'null') {
                    var local_cmi = JSON.parse(cmi_str);
                    if (local_cmi && typeof local_cmi === 'object'){
                        // Merge localStorage data with existing CMI data
                        // But preserve the suspend_data if it's already set from resume point
                        const preserveSuspendData = this.cmi['cmi.suspend_data'] &&
                                                   this.cmi['cmi.suspend_data'] !== '' &&
                                                   this.cmi['cmi.suspend_data'] !== '}';

                        Object.keys(local_cmi).forEach(key => {
                            // Don't overwrite suspend_data if we have valid data from resume point
                            if (key === 'cmi.suspend_data' && preserveSuspendData) {
                                console.log('SCORM: Preserving suspend_data from resume point');
                                return;
                            }
                            this.cmi[key] = local_cmi[key];
                        });
                    }
                }
            } catch (error) {
                console.warn('SCORM: Error parsing localStorage data, using defaults', error);
            }

            console.log('SCORM: LMSInitialize completed', this.cmi);
            return this.scormbool(true);
        }

        LMSFinish = (empty) => {
            window.localStorage.removeItem(this.scormId);
            return this.scormbool(true);
        }

        LMSGetValue = (element) => {
            return this.cmi[element] === undefined ? '' : this.cmi[element];
        }

        LMSSetValue = (element, value) => {
            this.cmi[element] = value;
            window.localStorage.setItem(this.scormId, JSON.stringify(this.cmi));

            // Send the entire CMI object to Flutter
            sendBack(this.cmi);

            return value;
        }

        LMSCommit = (empty) => {
            return this.scormbool(true);
        }

        LMSGetLastError = () => {
            return this.lastError;
        }

        LMSGetErrorString = (errorCode) => {
            this.lastError = errorCode;
            return ScormErrors[errorCode];
        }

        LMSGetDiagnostic = (errorCode) => {
            this.lastError = errorCode;
            return ScormErrors[errorCode];
        }
    }

    window.contents = {};
    window.add_content_to_window = function(contentId) {
        window.contents[contentId] = document.getElementById(contentId);
    };

    const sendBack = function(data) {
        try {
            const payload = JSON.stringify(data);
            console.log("Sending data to Flutter - payload length:", payload.length);

            // Verify that suspend_data is preserved correctly
            if (data['cmi.suspend_data']) {
                console.log("SCORM: suspend_data length in payload:", data['cmi.suspend_data'].length);
                console.log("SCORM: suspend_data preview:", data['cmi.suspend_data'].substring(0, 50) + '...');
            }

            messageHandler.postMessage(payload);
        } catch (err) {
            console.error("Failed to send data to Flutter:", err);
            console.error("Data that failed to stringify:", data);

            // Try to send a minimal version without the problematic data
            try {
                const minimalData = {
                    'cmi.core.lesson_status': data['cmi.core.lesson_status'] || '',
                    'cmi.core.lesson_location': data['cmi.core.lesson_location'] || '',
                    'cmi.core.score.raw': data['cmi.core.score.raw'] || '',
                    'error': 'Failed to stringify full CMI data'
                };
                messageHandler.postMessage(JSON.stringify(minimalData));
            } catch (fallbackErr) {
                console.error("Even minimal data failed to stringify:", fallbackErr);
            }
        }
    };

    const initializeScormApi = function(event) {
        if (!window.API) {
            window.API = new ScormAdapter();
        }

        for (const contentId of Object.keys(window.contents)) {
            const content = window.contents[contentId];
            content.setAttribute('src', content.getAttribute('data-src'));
        }
    };

    window.add_content_to_window('scormPlayer');
    window.addEventListener('load', initializeScormApi);
</script>

</body>
</html>
